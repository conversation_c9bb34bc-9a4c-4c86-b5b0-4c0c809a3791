generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}



model blogposts {
  id               BigInt    @id @default(autoincrement())
  authorid         String?
  title            String
  content          String
  slug             String    @unique
  featuredimageurl String?
  excerpt          String?
  ispublished      Boolean?  @default(false)
  publishedat      DateTime? @db.Timestamptz(6)
  categories       String?
  tags             String?
  createdat        DateTime  @default(now()) @db.Timestamptz(6)
  updatedat        DateTime? @db.Timestamptz(6)

  @@index([ispublished], map: "ix_blogposts_ispublished")
  @@index([publishedat], map: "ix_blogposts_publishedat")
}

model categories {
  id           BigInt     @id @default(autoincrement())
  categname    String     @db.VarChar(50)
  categdesc    String?
  parentid     Int        @default(0)
  isactive     Boolean    @default(true)
  displayorder Int?       @default(0)
  createdat    DateTime   @default(now()) @db.Timestamptz(6)
  updatedat    DateTime?  @db.Timestamptz(6)
  services     services[]
}

model chatbotintents {
  id                  BigInt                @id @default(autoincrement())
  name                String
  displayname         String
  description         String?
  isactive            Boolean               @default(true)
  priority            Int?                  @default(1)
  displayorder        Int                   @default(0)
  iconclass           String?
  createdat           DateTime              @default(now()) @db.Timestamptz(6)
  updatedat           DateTime?             @db.Timestamptz(6)
  chatbotkeywords     chatbotkeywords[]
  chatbotquickactions chatbotquickactions[]
  chatbotresponses    chatbotresponses[]
}

model chatbotkeywords {
  id              BigInt         @id @default(autoincrement())
  keyword         String
  synonyms        String?
  weight          Int            @default(1)
  isactive        Boolean        @default(true)
  matchtype       String         @default("contains")
  chatbotintentid BigInt
  createdat       DateTime       @default(now()) @db.Timestamptz(6)
  updatedat       DateTime?      @db.Timestamptz(6)
  chatbotintents  chatbotintents @relation(fields: [chatbotintentid], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([chatbotintentid], map: "ix_chatbotkeywords_chatbotintentid")
}

model chatbotquickactions {
  id                BigInt            @id @default(autoincrement())
  text              String
  actiontype        String            @default("message")
  actionvalue       String
  url               String?
  isactive          Boolean           @default(true)
  displayorder      Int               @default(0)
  cssclass          String?
  chatbotresponseid BigInt?
  chatbotintentid   BigInt?
  createdat         DateTime          @default(now()) @db.Timestamptz(6)
  updatedat         DateTime?         @db.Timestamptz(6)
  chatbotintents    chatbotintents?   @relation(fields: [chatbotintentid], references: [id], onUpdate: NoAction)
  chatbotresponses  chatbotresponses? @relation(fields: [chatbotresponseid], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([chatbotintentid], map: "ix_chatbotquickactions_chatbotintentid")
  @@index([chatbotresponseid], map: "ix_chatbotquickactions_chatbotresponseid")
}

model chatbotresponses {
  id                  BigInt                @id @default(autoincrement())
  responsetext        String
  responsetype        String                @default("text")
  isactive            Boolean               @default(true)
  displayorder        Int                   @default(0)
  conditions          String?
  templatevariables   String?
  chatbotintentid     BigInt
  createdat           DateTime              @default(now()) @db.Timestamptz(6)
  updatedat           DateTime?             @db.Timestamptz(6)
  chatbotquickactions chatbotquickactions[]
  chatbotintents      chatbotintents        @relation(fields: [chatbotintentid], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([chatbotintentid], map: "ix_chatbotresponses_chatbotintentid")
}

model clients {
  id              BigInt         @id @default(autoincrement())
  userid          BigInt?
  companyname     String
  contactname     String
  contactposition String?
  contactemail    String
  contactphone    String?
  contactfax      String?
  companywebsite  String?
  address         String?
  city            String?
  state           String?
  zipcode         String?
  country         String?
  logourl         String?
  createdat       DateTime       @default(now()) @db.Timestamptz(6)
  updatedat       DateTime?      @db.Timestamptz(6)
  isactive        Boolean        @default(true)
  notes           String?
  users           users?         @relation(fields: [userid], references: [id], onUpdate: NoAction)
  contracts       contracts[]
  feedbacks       feedbacks[]
  invoices        invoices[]
  orders          orders[]
  projects        projects[]
  testimonials    testimonials[]
}

model contactforms {
  id        BigInt    @id @default(autoincrement())
  name      String
  email     String
  phone     String?
  subject   String
  message   String
  isread    Boolean?  @default(false)
  readat    DateTime? @db.Timestamptz(6)
  status    String    @default("New")
  createdat DateTime  @default(now()) @db.Timestamptz(6)
  updatedat DateTime? @db.Timestamptz(6)

  @@index([status], map: "ix_contactforms_status")
}

model contracts {
  id                                                BigInt       @id @default(autoincrement())
  contname                                          String
  projid                                            BigInt
  clientid                                          BigInt
  orderid                                           BigInt
  contmanager                                       BigInt?
  contservtype                                      String?
  contlang                                          String?
  agreementdesc                                     String?
  contvalue                                         Float?
  contvaluecurr                                     String?
  billingtype                                       String?
  nextbilldate                                      DateTime?    @db.Timestamptz(6)
  contsignmethod                                    String?
  contsigneddate                                    DateTime?    @db.Timestamptz(6)
  contexecuteddate                                  DateTime?    @db.Timestamptz(6)
  contexpirydate                                    DateTime?    @db.Timestamptz(6)
  contstatus                                        String?      @default("DRAFT")
  lastupdatedate                                    DateTime?    @db.Timestamptz(6)
  lastupdateuser                                    BigInt?
  contfile                                          String?
  fileuploaddate                                    DateTime?    @db.Timestamptz(6)
  comments                                          String?
  notes                                             String?
  createdat                                         DateTime?    @default(now()) @db.Timestamptz(6)
  updatedat                                         DateTime?    @db.Timestamptz(6)
  clients                                           clients      @relation(fields: [clientid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  teammembers_contracts_contmanagerToteammembers    teammembers? @relation("contracts_contmanagerToteammembers", fields: [contmanager], references: [id], onDelete: Cascade, onUpdate: NoAction)
  teammembers_contracts_lastupdateuserToteammembers teammembers? @relation("contracts_lastupdateuserToteammembers", fields: [lastupdateuser], references: [id], onDelete: Cascade, onUpdate: NoAction)
  orders                                            orders       @relation(fields: [orderid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  projects                                          projects     @relation(fields: [projid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  invoices                                          invoices[]
}

model datauploadlogs {
  id          BigInt    @id @default(autoincrement())
  filename    String?
  entitytype  String?
  operation   String?
  recordcount Int?
  status      String?
  errorlog    String?
  uploadedby  String?
  createdat   DateTime? @default(now()) @db.Timestamptz(6)
}

model feedbacks {
  id            BigInt    @id @default(autoincrement())
  subject       String
  comment       String
  feedbacktype  String
  rating        Int?
  clientname    String?
  clientemail   String
  priority      String    @default("Medium")
  status        String    @default("New")
  isread        Boolean?  @default(false)
  readat        DateTime? @db.Timestamptz(6)
  resolvedat    DateTime? @db.Timestamptz(6)
  adminresponse String?
  adminname     String?
  responsedate  DateTime? @db.Timestamptz(6)
  clientid      BigInt?
  projectid     BigInt?
  ispublic      Boolean?  @default(false)
  createdat     DateTime  @default(now()) @db.Timestamptz(6)
  updatedat     DateTime? @db.Timestamptz(6)
  clients       clients?  @relation(fields: [clientid], references: [id], onUpdate: NoAction)
  projects      projects? @relation(fields: [projectid], references: [id], onUpdate: NoAction)

  @@index([clientid], map: "ix_feedbacks_clientid")
  @@index([projectid], map: "ix_feedbacks_projectid")
  @@index([status], map: "ix_feedbacks_status")
}



model invoiceitems {
  id          BigInt    @id @default(autoincrement())
  description String
  quantity    Decimal   @db.Decimal(18, 2)
  unitprice   Decimal   @db.Decimal(18, 2)
  totalprice  Decimal   @db.Decimal(18, 2)
  invoiceid   BigInt
  createdat   DateTime  @default(now()) @db.Timestamptz(6)
  updatedat   DateTime? @db.Timestamptz(6)
  invoices    invoices  @relation(fields: [invoiceid], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([invoiceid], map: "ix_invoiceitems_invoiceid")
}

model invoices {
  id           BigInt         @id @default(autoincrement())
  duedate      DateTime       @db.Timestamptz(6)
  subtotal     Decimal?       @db.Decimal
  taxrate      Decimal        @default(0) @db.Decimal(5, 2)
  taxamount    Decimal        @default(0) @db.Decimal(18, 2)
  totalamount  Decimal        @db.Decimal(18, 2)
  status       String         @default("Pending")
  description  String?
  clientid     BigInt
  contid       BigInt
  orderid      BigInt
  projectid    BigInt?
  paidat       DateTime?      @db.Timestamptz(6)
  createdat    DateTime       @default(now()) @db.Timestamptz(6)
  updatedat    DateTime?      @db.Timestamptz(6)
  invoiceitems invoiceitems[]
  clients      clients        @relation(fields: [clientid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  contracts    contracts      @relation(fields: [contid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  orders       orders         @relation(fields: [orderid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  projects     projects?      @relation(fields: [projectid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  payments     payments[]

  @@index([clientid], map: "ix_invoices_clientid")
  @@index([duedate], map: "ix_invoices_duedate")
  @@index([orderid], map: "ix_invoices_orderid")
  @@index([status], map: "ix_invoices_status")
}

model jobapplications {
  id             BigInt      @id @default(autoincrement())
  applicantname  String
  applicantemail String
  applicantphone String?
  resumeurl      String?
  coverletter    String?
  status         String      @default("PENDING")
  notes          String?
  joblistingid   BigInt
  createdat      DateTime    @default(now()) @db.Timestamptz(6)
  updatedat      DateTime?   @db.Timestamptz(6)
  joblistings    joblistings @relation(fields: [joblistingid], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([joblistingid], map: "ix_jobapplications_joblistingid")
}

model joblistings {
  id              BigInt            @id @default(autoincrement())
  title           String
  description     String
  requirements    String
  location        String
  employmenttype  String
  salarymin       Decimal?          @db.Decimal(18, 2)
  salarymax       Decimal?          @db.Decimal(18, 2)
  salarycurrency  String?           @default("USD")
  isremote        Boolean?          @default(false)
  isactive        Boolean           @default(true)
  expiresat       DateTime?         @db.Timestamptz(6)
  createdat       DateTime          @default(now()) @db.Timestamptz(6)
  updatedat       DateTime?         @db.Timestamptz(6)
  jobapplications jobapplications[]

  @@index([expiresat], map: "ix_joblistings_expiresat")
  @@index([isactive], map: "ix_joblistings_isactive")
}

model legalpages {
  id                BigInt              @id @default(autoincrement())
  title             String
  slug              String              @unique
  metadescription   String?
  content           String
  isactive          Boolean             @default(true)
  displayorder      Int?                @default(0)
  lastmodified      DateTime            @default(now()) @db.Timestamptz(6)
  modifiedby        String?
  createdat         DateTime            @default(now()) @db.Timestamptz(6)
  updatedat         DateTime?           @db.Timestamptz(6)
  legalpagesections legalpagesections[]
}

model legalpagesections {
  id           BigInt     @id @default(autoincrement())
  legalpageid  BigInt
  title        String
  content      String
  iconclass    String?
  displayorder Int        @default(0)
  isactive     Boolean    @default(true)
  createdat    DateTime   @default(now()) @db.Timestamptz(6)
  updatedat    DateTime?  @db.Timestamptz(6)
  legalpages   legalpages @relation(fields: [legalpageid], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([legalpageid], map: "ix_legalpagesections_legalpageid")
}

model messages {
  id         BigInt    @id @default(autoincrement())
  content    String
  sendername String
  senderrole String
  senderid   String
  isread     Boolean?  @default(false)
  readat     DateTime? @db.Timestamptz(6)
  projectid  BigInt
  createdat  DateTime  @default(now()) @db.Timestamptz(6)
  updatedat  DateTime? @db.Timestamptz(6)
  projects   projects  @relation(fields: [projectid], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([projectid], map: "ix_messages_projectid")
}

model orderdetails {
  id                    BigInt                 @id @default(autoincrement())
  orderid               BigInt
  servid                BigInt
  optid                 BigInt?
  featid                BigInt?
  costeach              Float?
  discountrate          Int?
  totaldiscount         Float?
  comments              String?
  notes                 String?
  isactive              Boolean                @default(true)
  createdat             DateTime?              @default(now()) @db.Timestamptz(6)
  updatedat             DateTime?              @db.Timestamptz(6)
  serviceoptionfeatures serviceoptionfeatures? @relation(fields: [featid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  serviceoptions        serviceoptions?        @relation(fields: [optid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  orders                orders                 @relation(fields: [orderid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  services              services               @relation(fields: [servid], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model orders {
  id                 BigInt         @id @default(autoincrement())
  ordertitle         String         @db.VarChar(40)
  clientid           BigInt
  ordermanager       BigInt?
  orderdesc          String?
  orderdate          DateTime?      @db.Timestamptz(6)
  ordertotalamount   Decimal?       @db.Decimal
  orderdiscountrate  Int?
  ordertotaldiscount Float?
  status             String?
  notes              String?
  isactive           Boolean        @default(true)
  createdat          DateTime?      @default(now()) @db.Timestamptz(6)
  updatedat          DateTime?      @db.Timestamptz(6)
  contracts          contracts[]
  invoices           invoices[]
  orderdetails       orderdetails[]
  clients            clients        @relation(fields: [clientid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  teammembers        teammembers?   @relation(fields: [ordermanager], references: [id], onDelete: Cascade, onUpdate: NoAction)
  projects           projects[]
}

model payments {
  id            BigInt    @id @default(autoincrement())
  amount        Decimal   @db.Decimal(18, 2)
  paymentdate   DateTime  @default(now()) @db.Timestamptz(6)
  paymentmethod String
  status        String    @default("Completed")
  notes         String?
  reference     String?
  transactionid String?
  processingfee Decimal?  @db.Decimal(18, 2)
  invoiceid     BigInt

  // Enhanced payment form fields
  currency      String    @default("USD")
  promocode     String?
  discount      Decimal?  @default(0) @db.Decimal(18, 2)
  emailreceipt  Boolean   @default(false)
  receiptemail  String?
  termsaccepted Boolean   @default(true)

  // Payment method specific fields (stored as JSON for flexibility)
  paymentdetails Json?    // Stores payment method specific data

  // Stripe specific fields
  stripepaymentintentid String?
  stripeclientsecret    String?

  createdat     DateTime  @default(now()) @db.Timestamptz(6)
  updatedat     DateTime? @db.Timestamptz(6)
  invoices      invoices  @relation(fields: [invoiceid], references: [id], onUpdate: NoAction)

  @@index([invoiceid], map: "ix_payments_invoiceid")
  @@index([paymentmethod], map: "ix_payments_paymentmethod")
  @@index([status], map: "ix_payments_status")
}

model payrollrecords {
  id           BigInt      @id @default(autoincrement())
  teammemberid BigInt
  paydate      DateTime?   @db.Timestamptz(6)
  workhours    Int?
  payperiod    String?
  basesalary   Decimal?    @db.Decimal
  payrate      Float?
  grosspay     Float?
  taxes        Float?
  overtime     Decimal?    @default(0) @db.Decimal
  bonuses      Decimal?    @default(0) @db.Decimal
  deduction    Float?
  netpay       Float?
  paymethod    String?     @db.VarChar(10)
  status       String?     @db.VarChar(10)
  notes        String?
  createdat    DateTime?   @default(now()) @db.Timestamptz(6)
  updatedat    DateTime?   @db.Timestamptz(6)
  teammembers  teammembers @relation(fields: [teammemberid], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model projectdocuments {
  id             BigInt    @id @default(autoincrement())
  filename       String
  fileurl        String
  filetype       String
  filesize       Int
  uploadedbyid   String
  uploadedbyname String
  projectid      BigInt
  createdat      DateTime  @default(now()) @db.Timestamptz(6)
  updatedat      DateTime? @db.Timestamptz(6)
  projects       projects  @relation(fields: [projectid], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([projectid], map: "ix_projectdocuments_projectid")
}

model projects {
  id                  BigInt                @id @default(autoincrement())
  name                String
  description         String
  projgoals           String?
  projmanager         BigInt?
  clientid            BigInt?
  orderid             BigInt
  imageurl            String?
  projecturl          String?
  githuburl           String?
  tags                String?
  projstartdate       DateTime?             @db.Timestamptz(6)
  projcompletiondate  DateTime?             @db.Timestamptz(6)
  estimatecost        Float?
  estimatetime        String?
  estimateeffort      String?
  status              String?
  isfeatured          Boolean?              @default(false)
  ispublic            Boolean?              @default(false)
  displayorder        Int                   @default(0)
  createdat           DateTime              @default(now()) @db.Timestamptz(6)
  updatedat           DateTime?             @db.Timestamptz(6)
  contracts           contracts[]
  feedbacks           feedbacks[]
  invoices            invoices[]
  messages            messages[]
  projectdocuments    projectdocuments[]
  clients             clients?              @relation(fields: [clientid], references: [id], onUpdate: NoAction)
  orders              orders                @relation(fields: [orderid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  teammembers         teammembers?          @relation(fields: [projmanager], references: [id], onDelete: Cascade, onUpdate: NoAction)
  projecttechnologies projecttechnologies[]
  tasks               tasks[]

  @@index([clientid], map: "ix_projects_clientid")
  @@index([isfeatured], map: "ix_projects_isfeatured")
  @@index([orderid], map: "ix_projects_orderid")
  @@index([projcompletiondate], map: "ix_projects_projcompletiondate")
}

model projecttechnologies {
  id             BigInt       @id @default(autoincrement())
  projectsid     BigInt
  technologiesid BigInt
  projects       projects     @relation(fields: [projectsid], references: [id], onDelete: Cascade, onUpdate: NoAction)
  technologies   technologies @relation(fields: [technologiesid], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model serviceoptionfeatures {
  id                BigInt         @id @default(autoincrement())
  optid             BigInt
  featname          String         @db.VarChar(50)
  featcost          Float?
  featdiscountrate  Int?
  feattotaldiscount Float?
  isincluded        Boolean?       @default(true)
  createdat         DateTime?      @default(now()) @db.Timestamptz(6)
  updatedat         DateTime?      @db.Timestamptz(6)
  featdesc          String?
  orderdetails      orderdetails[]
  serviceoptions    serviceoptions @relation(fields: [optid], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model serviceoptions {
  id                    BigInt                  @id @default(autoincrement())
  servid                BigInt
  optname               String                  @db.VarChar(50)
  optprice              Decimal?                @db.Decimal
  optdiscountrate       Int?
  opttotaldiscount      Float?
  optdesc               String?
  isactive              Boolean                 @default(true)
  createdat             DateTime?               @default(now()) @db.Timestamptz(6)
  updatedat             DateTime?               @db.Timestamptz(6)
  orderdetails          orderdetails[]
  serviceoptionfeatures serviceoptionfeatures[]
  services              services                @relation(fields: [servid], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model services {
  id             BigInt           @id @default(autoincrement())
  categid        BigInt
  name           String
  description    String
  iconclass      String?
  price          Decimal          @db.Decimal(18, 2)
  discountrate   Int?
  totaldiscount  Float?
  manager        String?
  isactive       Boolean          @default(true)
  displayorder   Int              @default(0)
  createdat      DateTime         @default(now()) @db.Timestamptz(6)
  updatedat      DateTime?        @db.Timestamptz(6)
  orderdetails   orderdetails[]
  serviceoptions serviceoptions[]
  categories     categories       @relation(fields: [categid], references: [id], onUpdate: NoAction)

  @@index([isactive], map: "ix_services_isactive")
}

model sitesettings {
  id          BigInt    @id @default(autoincrement())
  key         String    @unique
  value       String
  category    String    @default("GENERAL")
  ispublic    Boolean?  @default(true)
  isactive    Boolean   @default(true)
  description String?
  icon        String?
  createdat   DateTime  @default(now()) @db.Timestamptz(6)
  updatedat   DateTime? @db.Timestamptz(6)
}

model tasks {
  id            BigInt      @id @default(autoincrement())
  projno        BigInt
  teammemberid  BigInt
  taskdesc      String?
  taskstartdate DateTime?   @db.Timestamptz(6)
  taskenddate   DateTime?   @db.Timestamptz(6)
  workhours     Int?
  payrate       Float?
  status        String?     @db.VarChar(10)
  notes         String?
  createdat     DateTime?   @default(now()) @db.Timestamptz(6)
  updatedat     DateTime?   @db.Timestamptz(6)
  projects      projects    @relation(fields: [projno], references: [id], onDelete: Cascade, onUpdate: NoAction)
  teammembers   teammembers @relation(fields: [teammemberid], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model teammembers {
  id                                              BigInt           @id @default(autoincrement())
  name                                            String
  position                                        String
  birthdate                                       DateTime?        @db.Timestamptz(6)
  gender                                          String?
  maritalstatus                                   String?
  socialsecurityno                                String?
  hiredate                                        DateTime?        @db.Timestamptz(6)
  address                                         String?
  city                                            String?
  state                                           String?
  zipcode                                         String?
  country                                         String?
  phone                                           String
  salary                                          Float?
  payrollmethod                                   String?
  empresumeurl                                    String?
  notes                                           String?
  bio                                             String?
  photourl                                        String?
  email                                           String?
  linkedinurl                                     String?
  twitterurl                                      String?
  githuburl                                       String?
  displayorder                                    Int              @default(0)
  isactive                                        Boolean          @default(true)
  createdat                                       DateTime         @default(now()) @db.Timestamptz(6)
  updatedat                                       DateTime?        @db.Timestamptz(6)
  contracts_contracts_contmanagerToteammembers    contracts[]      @relation("contracts_contmanagerToteammembers")
  contracts_contracts_lastupdateuserToteammembers contracts[]      @relation("contracts_lastupdateuserToteammembers")
  orders                                          orders[]
  payrollrecords                                  payrollrecords[]
  projects                                        projects[]
  tasks                                           tasks[]

  @@index([isactive], map: "ix_teammembers_isactive")
}

model technologies {
  id                  BigInt                @id @default(autoincrement())
  name                String
  description         String
  iconurl             String?
  displayorder        Int                   @default(0)
  isactive            Boolean               @default(true)
  createdat           DateTime              @default(now()) @db.Timestamptz(6)
  updatedat           DateTime?             @db.Timestamptz(6)
  projecttechnologies projecttechnologies[]
}

model testimonials {
  id             BigInt    @id @default(autoincrement())
  clientid       BigInt
  clientname     String
  clienttitle    String
  clientcompany  String
  clientphotourl String?
  content        String
  rating         Int       @default(5)
  isfeatured     Boolean?  @default(false)
  displayorder   Int       @default(0)
  createdat      DateTime  @default(now()) @db.Timestamptz(6)
  updatedat      DateTime? @db.Timestamptz(6)
  clients        clients   @relation(fields: [clientid], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([isfeatured], map: "ix_testimonials_isfeatured")
}

model users {
  id            BigInt    @id @default(autoincrement())
  email         String    @unique
  emailverified DateTime? @db.Timestamptz(6)
  password      String?
  firstname     String?
  lastname      String?
  imageurl      String?
  role          userrole  @default(USER)
  isactive      Boolean   @default(true)
  createdat     DateTime  @default(now()) @db.Timestamptz(6)
  updatedat     DateTime? @db.Timestamptz(6)
  clients       clients[]
  accounts      Account[]
  sessions      Session[]
  auditlogs     auditlogs[]
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            BigInt
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user users @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       BigInt
  expires      DateTime
  user         users    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model auditlogs {
  id          BigInt    @id @default(autoincrement())
  userid      BigInt?
  action      String
  resource    String?
  resourceid  String?
  details     String?   @db.Text
  ipaddress   String?
  useragent   String?   @db.Text
  success     Boolean   @default(true)
  errormessage String?  @db.Text
  createdat   DateTime  @default(now()) @db.Timestamptz(6)

  user        users?    @relation(fields: [userid], references: [id], onDelete: SetNull)

  @@index([userid])
  @@index([action])
  @@index([resource])
  @@index([createdat])
}

enum UserRole {
  ADMIN
  USER
  CLIENT
}

enum ProjectStatus {
  PLANNING
  IN_PROGRESS
  COMPLETED
  ON_HOLD
  CANCELLED
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum OrderStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum ContactStatus {
  NEW
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum userrole {
  USER
  ADMIN
  MODERATOR
}

model staticcontent {
  id          BigInt    @id @default(autoincrement())
  page        String    // e.g., 'home', 'about', 'services', 'contact'
  section     String    // e.g., 'hero', 'services', 'cta', 'newsletter'
  contentkey  String    // e.g., 'title', 'subtitle', 'description', 'buttonText'
  contenttype String    @default("text") // 'text', 'html', 'url', 'email', 'phone'
  content     String    // The actual content
  displayorder Int      @default(0)
  isactive    Boolean   @default(true)
  createdat   DateTime  @default(now()) @db.Timestamptz(6)
  updatedat   DateTime? @db.Timestamptz(6)

  @@unique([page, section, contentkey])
  @@index([page, section])
  @@index([isactive])
}

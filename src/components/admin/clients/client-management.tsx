'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  BuildingOfficeIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  DocumentTextIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  CreditCardIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  UserIcon,
  PhoneIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  MapPinIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import { ClientModal } from './client-modal'

// Utility functions
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  logoUrl?: string
  isActive: boolean
  notes?: string
  createdAt: string
  updatedAt: string
  _count?: {
    projects: number
    contracts: number
    invoices: number
    payments: number
  }
}

interface ClientManagementProps {
  selectedClient: Client | null
  onClientSelect: (client: Client | null) => void
}

export function ClientManagement({ selectedClient, onClientSelect }: ClientManagementProps) {
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedClients, setSelectedClients] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingClient, setEditingClient] = useState<Client | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // View controls state
  const [viewMode, setViewMode] = useState('list')
  const [displayDensity, setDisplayDensity] = useState('comfortable')
  const [visibleColumns, setVisibleColumns] = useState([
    'companyName', 'contactName', 'contactEmail', 'contactPhone', 'city', 'updatedAt', 'isActive'
  ])
  const [sortBy, setSortBy] = useState('updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [filters, setFilters] = useState<Record<string, string>>({})
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnControls, setShowColumnControls] = useState(false)

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch clients
  const fetchClients = async (preserveFocus = false) => {
    try {
      if (!preserveFocus) {
        setLoading(true)
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        sortBy: sortBy,
        sortOrder: sortOrder,
        ...(debouncedSearchQuery && { search: debouncedSearchQuery }),
      })

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value)
        }
      })

      const response = await fetch(`/api/admin/clients?${params}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch clients: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setClients(data.data || [])
        setTotalPages(Math.ceil((data.total || 0) / 20))
      } else {
        throw new Error(data.error || 'Failed to fetch clients')
      }
      setError(null)
    } catch (err) {
      console.error('Error fetching clients:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setClients([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const isSearching = debouncedSearchQuery !== ''
    fetchClients(isSearching)
  }, [currentPage, debouncedSearchQuery, sortBy, sortOrder, filters])

  // Handle create
  const handleCreate = async (formData: any) => {
    try {
      const response = await fetch('/api/admin/clients', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) throw new Error('Failed to create client')

      setIsCreateModalOpen(false)
      fetchClients()
      alert('Client created successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create client'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle update
  const handleUpdate = async (id: string, formData: any) => {
    try {
      const response = await fetch(`/api/admin/clients/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to update client (${response.status})`)
      }

      setIsEditModalOpen(false)
      setEditingClient(null)
      fetchClients()
      alert('Client updated successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update client'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/clients/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete client')
      }

      setError(null)
      fetchClients()
      console.log('Client deleted successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete client'
      setError(errorMessage)
      console.error('Delete error:', err)
    }
  }

  // Handle action
  const handleAction = async (action: string, item: Client) => {
    const actionKey = `${action}-${item.id}`

    try {
      setActionLoading(actionKey)

      switch (action) {
        case 'view':
          window.open(`/admin/clients/${item.id}`, '_blank')
          break

        case 'edit':
          setEditingClient(item)
          setIsEditModalOpen(true)
          break

        case 'toggle-status':
          await handleToggleStatus(String(item.id), !item.isActive)
          break

        case 'delete':
          const confirmMessage = 'Are you sure you want to delete this client? This action cannot be undone.'
          if (window.confirm(confirmMessage)) {
            await handleDelete(String(item.id))
          }
          break

        case 'select':
          onClientSelect(item)
          break

        default:
          console.warn(`Unknown action: ${action}`)
      }
    } finally {
      setActionLoading(null)
    }
  }

  // Toggle client status
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/clients/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive }),
      })

      if (!response.ok) throw new Error('Failed to update client status')

      fetchClients()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update client status')
    }
  }

  // Handle bulk actions
  const handleBulkAction = async (action: string, clientIds: string[]) => {
    try {
      let endpoint = '/api/admin/clients'
      let method = 'PUT'
      let body: any = { ids: clientIds }

      switch (action) {
        case 'activate':
          body.data = { isActive: true }
          break
        case 'deactivate':
          body.data = { isActive: false }
          break
        case 'delete':
          method = 'DELETE'
          body = { ids: clientIds }
          break
        default:
          throw new Error(`Unknown bulk action: ${action}`)
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to ${action} clients`)
      }

      const result = await response.json()

      if (result.success) {
        setSelectedClients([])
        fetchClients()
      } else {
        throw new Error(result.error || `Failed to ${action} clients`)
      }
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error)
      throw error
    }
  }

  // Handle select all
  const handleSelectAll = () => {
    if (selectedClients.length === clients.length) {
      setSelectedClients([])
    } else {
      setSelectedClients(clients.map(client => String(client.id)))
    }
  }

  // Handle select individual client
  const handleSelectClient = (clientId: string) => {
    setSelectedClients(prev =>
      prev.includes(clientId)
        ? prev.filter(id => id !== clientId)
        : [...prev, clientId]
    )
  }

  // View control functions
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const handleColumnToggle = (columnKey: string) => {
    const isVisible = visibleColumns.includes(columnKey)
    if (isVisible) {
      setVisibleColumns(prev => prev.filter(col => col !== columnKey))
    } else {
      setVisibleColumns(prev => [...prev, columnKey])
    }
  }

  const resetViewSettings = () => {
    setVisibleColumns(['companyName', 'contactName', 'contactEmail', 'contactPhone', 'city', 'updatedAt', 'isActive'])
    setViewMode('list')
    setDisplayDensity('comfortable')
    setSortBy('updatedAt')
    setSortOrder('desc')
    setFilters({})
  }

  const availableColumns = [
    { key: 'companyName', label: 'Company Name', hideable: false },
    { key: 'contactName', label: 'Contact Name', hideable: true },
    { key: 'contactEmail', label: 'Email', hideable: true },
    { key: 'contactPhone', label: 'Phone', hideable: true },
    { key: 'website', label: 'Website', hideable: true },
    { key: 'city', label: 'City', hideable: true },
    { key: 'country', label: 'Country', hideable: true },
    { key: 'updatedAt', label: 'Last Updated', hideable: true },
    { key: 'isActive', label: 'Status', hideable: true },
  ]

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    card: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <div className="text-red-600 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-red-900 mb-2">Error Loading Clients</h3>
          <p className="text-sm text-red-700 mb-4">{error}</p>
          <button
            onClick={fetchClients}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Client Management</h2>
          <p className="text-sm text-gray-600 mt-1">
            Select a client to manage their projects, invoices, and payments
          </p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Client</span>
        </button>
      </div>

      {/* Enhanced Controls Bar */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 flex-1">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Search clients by name, email, company..."
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium transition-colors ${
                showFilters || Object.values(filters).some(v => v)
                  ? 'bg-blue-50 text-blue-700 border-blue-300'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filters
              {Object.values(filters).some(v => v) && (
                <span className="ml-2 bg-blue-100 text-blue-800 text-xs rounded-full px-2 py-0.5">
                  {Object.values(filters).filter(v => v).length}
                </span>
              )}
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2">
            {/* Bulk Actions */}
            {selectedClients.length > 0 && (
              <div className="flex items-center space-x-2 mr-4">
                <span className="text-sm text-gray-500">
                  {selectedClients.length} selected
                </span>
                <button
                  onClick={() => handleBulkAction('activate', selectedClients)}
                  className="px-3 py-1 bg-green-100 text-green-800 rounded-md text-sm hover:bg-green-200"
                >
                  Activate
                </button>
                <button
                  onClick={() => handleBulkAction('deactivate', selectedClients)}
                  className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-md text-sm hover:bg-yellow-200"
                >
                  Deactivate
                </button>
              </div>
            )}

            {/* View Mode Controls */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              {Object.entries(viewModeIcons).map(([mode, Icon]) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode)}
                  className={`p-1.5 rounded-md transition-colors ${
                    viewMode === mode
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                >
                  <Icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* Density Control */}
            <div className="relative">
              <button
                onClick={() => setDisplayDensity(displayDensity === 'compact' ? 'comfortable' : 'compact')}
                className="p-2 text-gray-500 hover:text-gray-700 border border-gray-300 rounded-lg"
                title={`Switch to ${displayDensity === 'compact' ? 'comfortable' : 'compact'} density`}
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
              </button>
            </div>

            {/* Column Controls */}
            <div className="relative">
              <button
                onClick={() => setShowColumnControls(!showColumnControls)}
                className="p-2 text-gray-500 hover:text-gray-700 border border-gray-300 rounded-lg"
                title="Column visibility"
              >
                <EyeIcon className="h-4 w-4" />
              </button>

              {showColumnControls && (
                <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                  <div className="p-3">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-sm font-medium text-gray-900">Show Columns</h3>
                      <button
                        onClick={resetViewSettings}
                        className="text-xs text-blue-600 hover:text-blue-800"
                      >
                        Reset
                      </button>
                    </div>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {availableColumns.map((column) => (
                        <label key={column.key} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={visibleColumns.includes(column.key)}
                            onChange={() => handleColumnToggle(column.key)}
                            disabled={!column.hideable}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className={`ml-2 text-sm ${!column.hideable ? 'text-gray-400' : 'text-gray-700'}`}>
                            {column.label}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={filters.isActive || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, isActive: e.target.value }))}
                  className="block w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Status</option>
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
                <input
                  type="text"
                  value={filters.city || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, city: e.target.value }))}
                  className="block w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Filter by city"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Country</label>
                <input
                  type="text"
                  value={filters.country || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, country: e.target.value }))}
                  className="block w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Filter by country"
                />
              </div>
            </div>
            {Object.values(filters).some(v => v) && (
              <div className="mt-3 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">Active filters:</span>
                  {Object.entries(filters).map(([key, value]) => value && (
                    <span
                      key={key}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {key}: {value}
                      <button
                        onClick={() => setFilters(prev => ({ ...prev, [key]: '' }))}
                        className="ml-1 text-blue-600 hover:text-blue-800"
                      >
                        <XMarkIcon className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
                <button
                  onClick={() => setFilters({})}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  Clear all
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Stats Overview */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{clients.length}</div>
            <div className="text-sm text-gray-600">Total Clients</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {clients.filter(c => c.isActive).length}
            </div>
            <div className="text-sm text-gray-600">Active</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {clients.reduce((sum, client) => sum + (client._count?.projects || 0), 0)}
            </div>
            <div className="text-sm text-gray-600">Projects</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {clients.reduce((sum, client) => sum + (client._count?.invoices || 0), 0)}
            </div>
            <div className="text-sm text-gray-600">Invoices</div>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        {/* Header with bulk actions */}
        {selectedClients.length > 0 && (
          <div className="px-6 py-3 border-b border-gray-200 bg-blue-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-blue-900">
                  {selectedClients.length} client{selectedClients.length !== 1 ? 's' : ''} selected
                </span>
                <button
                  onClick={() => setSelectedClients([])}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Clear selection
                </button>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleBulkAction('activate', selectedClients)}
                  className="px-3 py-1 bg-green-600 text-white rounded-md text-sm hover:bg-green-700"
                >
                  Activate
                </button>
                <button
                  onClick={() => handleBulkAction('deactivate', selectedClients)}
                  className="px-3 py-1 bg-yellow-600 text-white rounded-md text-sm hover:bg-yellow-700"
                >
                  Deactivate
                </button>
                <button
                  onClick={() => {
                    if (confirm('Are you sure you want to delete the selected clients?')) {
                      handleBulkAction('delete', selectedClients)
                    }
                  }}
                  className="px-3 py-1 bg-red-600 text-white rounded-md text-sm hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}



        {/* Content */}
        <div className={`${displayDensity === 'compact' ? 'p-4' : 'p-6'}`}>
          {clients.length === 0 ? (
            <div className="text-center py-12">
              <BuildingOfficeIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No clients found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {debouncedSearchQuery || Object.values(filters).some(v => v)
                  ? 'Try adjusting your search terms or filters.'
                  : 'Get started by adding your first client.'}
              </p>
              <div className="mt-6">
                <button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Client
                </button>
              </div>
            </div>
          ) : viewMode === 'list' ? (
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="relative px-6 py-3">
                      <input
                        type="checkbox"
                        checked={selectedClients.length === clients.length && clients.length > 0}
                        onChange={handleSelectAll}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </th>
                    {visibleColumns.includes('companyName') && (
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Company
                      </th>
                    )}
                    {visibleColumns.includes('contactEmail') && (
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Email
                      </th>
                    )}
                    {visibleColumns.includes('contactPhone') && (
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Phone
                      </th>
                    )}
                    {visibleColumns.includes('city') && (
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Location
                      </th>
                    )}
                    {visibleColumns.includes('updatedAt') && (
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Updated
                      </th>
                    )}
                    {visibleColumns.includes('isActive') && (
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    )}
                    <th scope="col" className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {clients.map((client) => (
                    <ClientRow
                      key={client.id}
                      client={client}
                      viewMode={viewMode}
                      displayDensity={displayDensity}
                      visibleColumns={visibleColumns}
                      isSelected={selectedClients.includes(String(client.id))}
                      onSelect={() => handleSelectClient(String(client.id))}
                      onAction={(action) => handleAction(action, client)}
                      actionLoading={actionLoading}
                      selectedClient={selectedClient}
                      onClientSelect={onClientSelect}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className={`
              ${viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' : ''}
              ${viewMode === 'card' ? 'grid grid-cols-1 lg:grid-cols-2 gap-6' : ''}
            `}>
              {clients.map((client) => (
                <ClientRow
                  key={client.id}
                  client={client}
                  viewMode={viewMode}
                  displayDensity={displayDensity}
                  visibleColumns={visibleColumns}
                  isSelected={selectedClients.includes(String(client.id))}
                  onSelect={() => handleSelectClient(String(client.id))}
                  onAction={(action) => handleAction(action, client)}
                  actionLoading={actionLoading}
                  selectedClient={selectedClient}
                  onClientSelect={onClientSelect}
                />
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-3 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <AnimatePresence>
        {isCreateModalOpen && (
          <ClientModal
            isOpen={isCreateModalOpen}
            onClose={() => setIsCreateModalOpen(false)}
            onSubmit={handleCreate}
            title="Add New Client"
          />
        )}
        {isEditModalOpen && editingClient && (
          <ClientModal
            isOpen={isEditModalOpen}
            onClose={() => {
              setIsEditModalOpen(false)
              setEditingClient(null)
            }}
            onSubmit={(data) => handleUpdate(String(editingClient.id), data)}
            title="Edit Client"
            initialData={editingClient}
          />
        )}
      </AnimatePresence>
    </div>
  )
}

// ClientRow Component
interface ClientRowProps {
  client: Client
  viewMode: string
  displayDensity: string
  visibleColumns: string[]
  isSelected: boolean
  onSelect: () => void
  onAction: (action: string) => void
  actionLoading: string | null
  selectedClient: Client | null
  onClientSelect: (client: Client | null) => void
}

function ClientRow({
  client,
  viewMode,
  displayDensity,
  visibleColumns,
  isSelected,
  onSelect,
  onAction,
  actionLoading,
  selectedClient,
  onClientSelect
}: ClientRowProps) {
  const isCompact = displayDensity === 'compact'
  const isCurrentlySelected = selectedClient?.id === client.id

  if (viewMode === 'list') {
    return (
      <tr
        className={`hover:bg-gray-50 cursor-pointer transition-colors ${
          isCurrentlySelected ? 'bg-blue-50' : ''
        }`}
        onClick={() => onClientSelect(client)}
      >
        <td className="px-6 py-4 whitespace-nowrap">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onSelect}
            onClick={(e) => e.stopPropagation()}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
        </td>

        {visibleColumns.includes('companyName') && (
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="flex items-center">
              <div className={`${isCompact ? 'h-8 w-8' : 'h-10 w-10'} bg-gray-100 rounded-lg flex items-center justify-center mr-3`}>
                <BuildingOfficeIcon className={`${isCompact ? 'h-4 w-4' : 'h-5 w-5'} text-gray-400`} />
              </div>
              <div className="ml-4">
                <button
                  onClick={() => onClientSelect(client)}
                  className="text-sm font-medium text-gray-900 hover:text-blue-600 text-left"
                >
                  {client.companyName}
                </button>
                <div className="text-xs text-gray-500">{client.contactName}</div>
              </div>
            </div>
          </td>
        )}

        {visibleColumns.includes('contactEmail') && (
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="flex items-center">
              <EnvelopeIcon className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm text-gray-900">{client.contactEmail}</span>
            </div>
          </td>
        )}

        {visibleColumns.includes('contactPhone') && (
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="flex items-center">
              <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm text-gray-900">{client.contactPhone || 'N/A'}</span>
            </div>
          </td>
        )}

        {visibleColumns.includes('city') && (
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="flex items-center">
              <MapPinIcon className="h-4 w-4 text-gray-400 mr-2" />
              <span className="text-sm text-gray-900">{client.city || 'N/A'}</span>
            </div>
          </td>
        )}

        {visibleColumns.includes('updatedAt') && (
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="text-xs text-gray-500">
              Updated {formatDate(client.updatedAt)}
            </div>
          </td>
        )}

        {visibleColumns.includes('isActive') && (
          <td className="px-6 py-4 whitespace-nowrap">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              client.isActive
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {client.isActive ? 'Active' : 'Inactive'}
            </span>
          </td>
        )}

        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex items-center justify-end space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation()
                onClientSelect(client)
              }}
              className="text-blue-600 hover:text-blue-900 p-1"
              title="View details"
            >
              <EyeIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('edit')
              }}
              disabled={actionLoading === `edit-${client.id}`}
              className="text-gray-400 hover:text-blue-600 p-1"
              title="Edit client"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('toggle-status')
              }}
              disabled={actionLoading === `toggle-status-${client.id}`}
              className="text-gray-400 hover:text-green-600 p-1"
              title={client.isActive ? 'Deactivate' : 'Activate'}
            >
              <PowerIcon className="h-4 w-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation()
                onAction('delete')
              }}
              disabled={actionLoading === `delete-${client.id}`}
              className="text-gray-400 hover:text-red-600 p-1"
              title="Delete client"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    )
  }

  // Grid and Card views
  return (
    <motion.div
      className={`bg-white border border-gray-200 rounded-lg ${isCompact ? 'p-4' : 'p-6'} hover:shadow-md transition-shadow cursor-pointer ${
        isCurrentlySelected ? 'ring-2 ring-blue-500 border-blue-500' : ''
      }`}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={() => onClientSelect(client)}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onSelect}
            onClick={(e) => e.stopPropagation()}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <div className={`${isCompact ? 'h-10 w-10' : 'h-12 w-12'} bg-gray-100 rounded-lg flex items-center justify-center`}>
            <BuildingOfficeIcon className={`${isCompact ? 'h-5 w-5' : 'h-6 w-6'} text-gray-400`} />
          </div>
          <div>
            <button
              onClick={() => onClientSelect(client)}
              className={`${isCompact ? 'text-base' : 'text-lg'} font-semibold text-gray-900 hover:text-blue-600 text-left block`}
            >
              {client.companyName}
            </button>
            <p className="text-sm text-gray-500">{client.contactName}</p>
          </div>
        </div>
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          client.isActive
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {client.isActive ? 'Active' : 'Inactive'}
        </span>
      </div>

      <div className="space-y-2 mb-4">
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <EnvelopeIcon className="h-4 w-4" />
          <span>{client.contactEmail}</span>
        </div>
        {client.contactPhone && (
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <PhoneIcon className="h-4 w-4" />
            <span>{client.contactPhone}</span>
          </div>
        )}
        {client.website && (
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <GlobeAltIcon className="h-4 w-4" />
            <span>{client.website}</span>
          </div>
        )}
        {client.city && (
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <MapPinIcon className="h-4 w-4" />
            <span>{client.city}, {client.country}</span>
          </div>
        )}
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <div className="flex items-center space-x-4 text-xs text-gray-500">
          <span>{client._count?.projects || 0} Projects</span>
          <span>{client._count?.invoices || 0} Invoices</span>
          <span>Updated {formatDate(client.updatedAt)}</span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={(e) => {
              e.stopPropagation()
              onClientSelect(client)
            }}
            className="text-blue-600 hover:text-blue-900 p-1"
            title="View details"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation()
              onAction('edit')
            }}
            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation()
              onAction('toggle-status')
            }}
            className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-md transition-colors"
            title={client.isActive ? 'Deactivate' : 'Activate'}
          >
            <PowerIcon className="h-4 w-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation()
              onAction('delete')
            }}
            className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </motion.div>
  )
}

'use client'

import React, { useState, useRef, useEffect } from 'react'
import { safeToLocaleDateString } from '@/lib/date-utils'

interface UserModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
  title: string
  initialData?: any
  fields?: any[]
  layout?: any
}

export function UserModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData
}: UserModalProps) {
  const [formData, setFormData] = useState({
    email: '',
    firstname: '',
    lastname: '',
    role: 'USER',
    imageurl: '',
    password: '',
    isactive: true,
    linkedclientid: '',
  })

  const [uploadingPhoto, setUploadingPhoto] = useState(false)
  const [availableClients, setAvailableClients] = useState<Array<{value: number, label: string}>>([])
  const [loadingClients, setLoadingClients] = useState(false)
  const photoInputRef = useRef<HTMLInputElement>(null)

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      console.log('User Modal - Loading initial data:', initialData)
      setFormData({
        email: initialData.email || '',
        firstname: initialData.firstname || '',
        lastname: initialData.lastname || '',
        role: initialData.role || 'USER',
        imageurl: initialData.imageurl || '',
        password: '', // Always empty for security
        isactive: initialData.isactive ?? true,
        linkedclientid: initialData.linkedclientid?.toString() || '',
      })
    } else {
      // Reset form for create mode - all fields empty
      setFormData({
        email: '',
        firstname: '',
        lastname: '',
        role: 'USER',
        imageurl: '',
        password: '',
        isactive: true,
        linkedclientid: '',
      })
    }
  }, [initialData])

  // Fetch available clients when role is CLIENT
  const fetchAvailableClients = async () => {
    if (formData.role !== 'CLIENT') {
      setAvailableClients([])
      return
    }

    setLoadingClients(true)
    try {
      const excludeUserId = initialData?.id ? `?excludeUserId=${initialData.id}` : ''
      const response = await fetch(`/api/admin/clients/available${excludeUserId}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setAvailableClients(data.data)
        } else {
          console.error('API returned success: false', data)
          setAvailableClients([])
        }
      } else {
        const errorData = await response.json()
        console.error('API error response:', errorData)
        setAvailableClients([])
      }
    } catch (error) {
      console.error('Error fetching available clients:', error)
      setAvailableClients([])
    } finally {
      setLoadingClients(false)
    }
  }

  // Fetch clients when role changes to CLIENT
  useEffect(() => {
    if (formData.role === 'CLIENT') {
      fetchAvailableClients()
    } else {
      setAvailableClients([])
      setFormData(prev => ({ ...prev, linkedclientid: '' }))
    }
  }, [formData.role, initialData?.id])

  const handlePhotoUpload = async (file: File) => {
    setUploadingPhoto(true)
    try {
      console.log('Starting photo upload:', file.name, file.type, file.size)

      const uploadFormData = new FormData()
      uploadFormData.append('file', file)

      console.log('Sending request to /api/upload/user-photo')
      const response = await fetch('/api/upload/user-photo', {
        method: 'POST',
        body: uploadFormData
      })

      console.log('Photo upload response status:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Photo upload failed with status:', response.status, 'Error:', errorText)
        throw new Error(`Upload failed: ${response.status} - ${errorText}`)
      }

      const result = await response.json()
      console.log('Photo upload result:', result)

      if (result.success) {
        console.log('Photo upload successful, setting photo URL:', result.data.url)
        setFormData(prev => ({ ...prev, imageurl: result.data.url }))
        alert(`Photo uploaded successfully! File saved to: ${result.data.url}`)
      } else {
        throw new Error(result.error || 'Upload failed')
      }
    } catch (error) {
      console.error('Photo upload error:', error)
      alert(`Failed to upload photo: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setUploadingPhoto(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      // Transform form data to match API expectations
      const submitData = {
        email: formData.email,
        firstname: formData.firstname,
        lastname: formData.lastname,
        role: formData.role,
        imageurl: formData.imageurl,
        isactive: formData.isactive,
        ...(formData.password && { password: formData.password }), // Only include password if provided
        ...(formData.linkedclientid && formData.linkedclientid !== '' && { linkedclientid: Number(formData.linkedclientid) }), // Only include if provided and not empty
      }

      await onSubmit(submitData)
      onClose()
    } catch (error) {
      console.error('Submit error:', error)
      alert('Failed to save user. Please try again.')
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" onClick={onClose} />

        <div className="relative bg-white rounded-xl shadow-2xl max-w-5xl w-full max-h-[95vh] overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-white">{title}</h2>
              <button
                type="button"
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Form Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Main Content Grid */}
              <div className="grid grid-cols-12 gap-6">

                {/* Left Column - User Information */}
                <div className="col-span-8 space-y-4">
                  {/* Account Credentials Card */}
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                      </svg>
                      Account Credentials
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      <div className="col-span-2">
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Email Address *
                        </label>
                        <input
                          type="email"
                          required
                          value={formData.email}
                          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter email address"
                        />
                      </div>

                      <div className="col-span-2">
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Password {initialData ? '(Leave empty to keep current)' : '*'}
                        </label>
                        <input
                          type="password"
                          required={!initialData}
                          value={formData.password}
                          onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder={initialData ? "Leave empty to keep current password" : "Enter password"}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Personal Information Card */}
                  <div className="bg-green-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      Personal Information
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          First Name
                        </label>
                        <input
                          type="text"
                          value={formData.firstname}
                          onChange={(e) => setFormData({ ...formData, firstname: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter first name"
                        />
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Last Name
                        </label>
                        <input
                          type="text"
                          value={formData.lastname}
                          onChange={(e) => setFormData({ ...formData, lastname: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Enter last name"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Role & Status Card */}
                  <div className="bg-purple-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                      Role & Status
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          User Role *
                        </label>
                        <select
                          required
                          value={formData.role}
                          onChange={(e) => setFormData({ ...formData, role: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="USER">User</option>
                          <option value="ADMIN">Administrator</option>
                          <option value="CLIENT">Client</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Account Status
                        </label>
                        <div className="flex items-center mt-2">
                          <input
                            type="checkbox"
                            id="isActive"
                            checked={formData.isactive}
                            onChange={(e) => setFormData({ ...formData, isactive: e.target.checked })}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
                          />
                          <label htmlFor="isActive" className="ml-2 text-xs text-gray-700">
                            Active account
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Client Linking Card - Only show for CLIENT role */}
                  {formData.role === 'CLIENT' && (
                    <div className="bg-green-50 rounded-lg p-4">
                      <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                        <svg className="h-4 w-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                        </svg>
                        Client Account Linking
                      </h3>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Linked Client (Optional)
                        </label>
                        <select
                          value={formData.linkedclientid}
                          onChange={(e) => setFormData({ ...formData, linkedclientid: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          disabled={loadingClients}
                        >
                          <option value="">Select a client to link (optional)</option>
                          {availableClients.map(client => (
                            <option key={client.value} value={client.value}>
                              {client.label}
                            </option>
                          ))}
                        </select>
                        {loadingClients && (
                          <p className="text-xs text-gray-500 mt-1">Loading available clients...</p>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                          Link this user account to a client record for dashboard access
                        </p>
                        {availableClients.length === 0 && !loadingClients && (
                          <p className="text-xs text-amber-600 mt-1">
                            No available clients to link. Create clients first or unlink existing client-user relationships.
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>



                {/* Right Column - Photo & Status */}
                <div className="col-span-4 space-y-4">
                  {/* Photo Upload Card */}
                  <div className="bg-orange-50 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                      <svg className="h-4 w-4 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      Profile Photo
                    </h3>

                    {/* Photo Preview */}
                    <div className="mb-3">
                      {formData.imageurl ? (
                        <div className="flex items-center justify-center w-16 h-16 bg-white border-2 border-dashed border-gray-300 rounded-lg">
                          <img
                            src={formData.imageurl}
                            alt="Profile photo"
                            className="w-12 h-12 object-cover rounded-lg"
                          />
                        </div>
                      ) : (
                        <div className="flex items-center justify-center w-16 h-16 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg">
                          <svg className="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                      )}
                    </div>

                    {/* Upload Button */}
                    <input
                      ref={photoInputRef}
                      type="file"
                      accept="image/png,image/jpeg,image/jpg,image/webp"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) {
                          handlePhotoUpload(file)
                        }
                      }}
                      className="hidden"
                    />
                    <button
                      type="button"
                      onClick={() => photoInputRef.current?.click()}
                      disabled={uploadingPhoto}
                      className="w-full flex items-center justify-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 transition-colors"
                    >
                      {uploadingPhoto ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-600 mr-2"></div>
                          Uploading...
                        </>
                      ) : (
                        <>
                          <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                          </svg>
                          Upload Photo
                        </>
                      )}
                    </button>

                    {/* Photo URL Display/Input */}
                    {formData.imageurl ? (
                      <div className="mt-3">
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Photo URL
                        </label>
                        <div className="flex">
                          <input
                            type="text"
                            value={formData.imageurl}
                            readOnly
                            className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-l-md bg-gray-50 text-gray-600"
                          />
                          <button
                            type="button"
                            onClick={() => setFormData({ ...formData, imageurl: '' })}
                            className="px-2 py-2 bg-red-100 text-red-600 border border-l-0 border-gray-300 rounded-r-md hover:bg-red-200 transition-colors"
                            title="Remove photo"
                          >
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="mt-3">
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Manual Photo URL (Optional)
                        </label>
                        <input
                          type="url"
                          value={formData.imageurl}
                          onChange={(e) => setFormData({ ...formData, imageurl: e.target.value })}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="https://example.com/photo.jpg"
                        />
                      </div>
                    )}

                    {/* View Photo Button */}
                    {formData.imageurl && (
                      <div className="mt-3">
                        <button
                          type="button"
                          onClick={() => window.open(formData.imageurl, '_blank')}
                          className="w-full flex items-center justify-center px-3 py-2 text-sm bg-orange-50 text-orange-700 border border-orange-200 rounded-md hover:bg-orange-100 transition-colors"
                        >
                          <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          View Photo
                        </button>
                      </div>
                    )}
                  </div>



                  {/* User Statistics Card */}
                  {initialData && (
                    <div className="bg-blue-50 rounded-lg p-4">
                      <h3 className="text-sm font-semibold text-gray-900 mb-3 flex items-center">
                        <svg className="h-4 w-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        User Statistics
                      </h3>
                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-600">User ID:</span>
                          <span className="font-medium">#{initialData.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Created:</span>
                          <span className="font-medium">{safeToLocaleDateString(initialData.createdat)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Last Updated:</span>
                          <span className="font-medium">{safeToLocaleDateString(initialData.updatedat)}</span>
                        </div>
                        {initialData._count && (
                          <>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Clients:</span>
                              <span className="font-medium">{initialData._count.clients || 0}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Audit Logs:</span>
                              <span className="font-medium">{initialData._count.auditlogs || 0}</span>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                <div className="text-xs text-gray-500">
                  {initialData ? 'Last updated: ' + safeToLocaleDateString(initialData.updatedat || Date.now()) : 'Creating new user'}
                </div>
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-md hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-sm"
                  >
                    {initialData ? (
                      <>
                        <svg className="h-4 w-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Update User
                      </>
                    ) : (
                      <>
                        <svg className="h-4 w-4 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Create User
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}

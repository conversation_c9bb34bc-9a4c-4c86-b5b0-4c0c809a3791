import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/api-utils'

// GET /api/client/dashboard - Get client dashboard data for authenticated user
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return Response.json(
      { success: false, message: 'Authentication required' },
      { status: 401 }
    )
  }

  if (session.user.role !== 'CLIENT') {
    return Response.json(
      { success: false, message: 'Access denied. Client role required.' },
      { status: 403 }
    )
  }

  // Find the client record linked to this user
  const client = await prisma.clients.findFirst({
    where: {
      userid: BigInt(session.user.id)
    },
    include: {
      projects: {
        select: {
          id: true,
          name: true,
          description: true,
          status: true,
          projstartdate: true,
          projcompletiondate: true,
          estimatecost: true,
          createdat: true,
          updatedat: true
        },
        orderBy: {
          createdat: 'desc'
        }
      },
      invoices: {
        select: {
          id: true,
          invoicenumber: true,
          invoicedate: true,
          duedate: true,
          totalamount: true,
          status: true,
          createdat: true,
          updatedat: true
        },
        orderBy: {
          createdat: 'desc'
        }
      },
      payments: {
        select: {
          id: true,
          amount: true,
          paymentdate: true,
          paymentmethod: true,
          status: true,
          createdat: true,
          updatedat: true
        },
        orderBy: {
          createdat: 'desc'
        }
      },
      _count: {
        select: {
          projects: true,
          invoices: true,
          payments: true
        }
      }
    }
  })

  if (!client) {
    return Response.json(
      { 
        success: false, 
        message: 'No client account linked to your user account. Please contact support.' 
      },
      { status: 404 }
    )
  }

  // Convert BigInt fields to numbers for JSON serialization
  const serializedClient = {
    id: Number(client.id),
    companyname: client.companyname,
    contactname: client.contactname,
    contactemail: client.contactemail,
    contactphone: client.contactphone,
    website: client.website,
    address: client.address,
    city: client.city,
    state: client.state,
    country: client.country,
    zipcode: client.zipcode,
    logourl: client.logourl,
    isactive: client.isactive,
    notes: client.notes,
    createdat: client.createdat,
    updatedat: client.updatedat,
    projects: client.projects.map(project => ({
      ...project,
      id: Number(project.id),
      estimatecost: project.estimatecost ? Number(project.estimatecost) : null
    })),
    invoices: client.invoices.map(invoice => ({
      ...invoice,
      id: Number(invoice.id),
      totalamount: invoice.totalamount ? Number(invoice.totalamount) : null
    })),
    payments: client.payments.map(payment => ({
      ...payment,
      id: Number(payment.id),
      amount: payment.amount ? Number(payment.amount) : null
    })),
    _count: client._count
  }

  return Response.json({
    success: true,
    data: serializedClient
  })
})

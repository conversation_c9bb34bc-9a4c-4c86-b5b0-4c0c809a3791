import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // Get all users with their basic info (no passwords)
    const users = await prisma.users.findMany({
      select: {
        id: true,
        email: true,
        firstname: true,
        lastname: true,
        role: true,
        isactive: true,
        createdat: true
      },
      orderBy: {
        createdat: 'desc'
      }
    })

    // Get all clients with their linked user info
    const clients = await prisma.clients.findMany({
      select: {
        id: true,
        companyname: true,
        contactname: true,
        contactemail: true,
        userid: true,
        isactive: true,
        createdat: true,
        users: {
          select: {
            id: true,
            email: true,
            role: true,
            isactive: true
          }
        }
      },
      orderBy: {
        createdat: 'desc'
      }
    })

    return Response.json({
      success: true,
      data: {
        users: users.map(user => ({
          ...user,
          id: Number(user.id)
        })),
        clients: clients.map(client => ({
          ...client,
          id: Number(client.id),
          userid: client.userid ? Number(client.userid) : null
        }))
      }
    })

  } catch (error) {
    console.error('Debug users error:', error)
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

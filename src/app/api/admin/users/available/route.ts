import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, requireAdmin } from '@/lib/api-utils'

// GET /api/admin/users/available - Get users available for linking (CLIENT role users not already linked)
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const url = new URL(request.url)
  const excludeClientId = url.searchParams.get('excludeClientId') // For edit mode

  // Get CLIENT role users that are not linked to any client, or linked to the current client being edited
  const where: any = {
    role: 'CLIENT',
    isactive: true,
    clients: {
      none: {} // Users with no linked clients
    }
  }

  // If editing a client, also include the user currently linked to this client
  if (excludeClientId) {
    const currentClient = await prisma.clients.findUnique({
      where: { id: BigInt(excludeClientId) },
      select: { userid: true }
    })

    if (currentClient?.userid) {
      where.OR = [
        { clients: { none: {} } }, // Users with no linked clients
        { id: currentClient.userid } // Or the user currently linked to this client
      ]
      delete where.clients
    }
  }

  const users = await prisma.users.findMany({
    where,
    select: {
      id: true,
      email: true,
      firstname: true,
      lastname: true
    },
    orderBy: {
      email: 'asc'
    }
  })

  // Convert BigInt to number for JSON serialization
  const serializedUsers = users.map(user => ({
    value: Number(user.id),
    label: `${user.email} (${user.firstname || ''} ${user.lastname || ''}`.trim() + ')',
    email: user.email
  }))

  return Response.json({
    success: true,
    data: serializedUsers
  })
})

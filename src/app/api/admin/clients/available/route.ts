import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, requireAdmin } from '@/lib/api-utils'

// GET /api/admin/clients/available - Get clients available for linking (not already linked to users)
export const GET = with<PERSON>rror<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  await requireAdmin(request)

  const url = new URL(request.url)
  const excludeUserId = url.searchParams.get('excludeUserId') // For edit mode

  // Get clients that are not linked to any user, or linked to the current user being edited
  const where: any = {
    isactive: true,
    OR: [
      { userid: null }, // Not linked to any user
      ...(excludeUserId ? [{ userid: BigInt(excludeUserId) }] : []) // Or linked to current user being edited
    ]
  }

  const clients = await prisma.clients.findMany({
    where,
    select: {
      id: true,
      companyname: true,
      contactname: true,
      contactemail: true
    },
    orderBy: {
      companyname: 'asc'
    }
  })

  // Convert BigInt to number for JSON serialization
  const serializedClients = clients.map(client => ({
    value: Number(client.id),
    label: `${client.companyname} (${client.contactname})`,
    email: client.contactemail
  }))

  return Response.json({
    success: true,
    data: serializedClients
  })
})

'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Link from 'next/link'
import { useTheme } from 'next-themes'
import {
  EyeIcon,
  EyeSlashIcon,
  EnvelopeIcon,
  LockClosedIcon,
  UserIcon,
  BuildingOfficeIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'
import { toast, Toaster } from 'react-hot-toast'

const signUpSchema = z.object({
  fullName: z
    .string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must be less than 100 characters')
    .regex(/^[a-zA-Z\s]+$/, 'Full name can only contain letters and spaces'),
  companyName: z
    .string()
    .min(2, 'Company name must be at least 2 characters')
    .max(100, 'Company name must be less than 100 characters'),
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .transform(val => val.toLowerCase().trim()),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  confirmPassword: z
    .string()
    .min(1, 'Please confirm your password'),
  agreeToTerms: z
    .boolean()
    .refine(val => val === true, 'You must agree to the terms and privacy policy'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type SignUpForm = z.infer<typeof signUpSchema>

export default function ClientSignUpPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [fieldValues, setFieldValues] = useState({
    fullName: false,
    companyName: false,
    email: false,
    password: false,
    confirmPassword: false
  })
  const router = useRouter()
  const { theme, setTheme } = useTheme()
  
  const fieldRefs = {
    fullName: useRef<HTMLInputElement>(null),
    companyName: useRef<HTMLInputElement>(null),
    email: useRef<HTMLInputElement>(null),
    password: useRef<HTMLInputElement>(null),
    confirmPassword: useRef<HTMLInputElement>(null)
  }

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<SignUpForm>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      agreeToTerms: false,
    },
  })

  const watchedFields = {
    fullName: watch('fullName'),
    companyName: watch('companyName'),
    email: watch('email'),
    password: watch('password'),
    confirmPassword: watch('confirmPassword')
  }

  const checkInputValue = (input: HTMLInputElement) => {
    return input.value.length > 0
  }

  // Check input values on mount and when they change
  useEffect(() => {
    Object.entries(fieldRefs).forEach(([key, ref]) => {
      if (ref.current) {
        setFieldValues(prev => ({
          ...prev,
          [key]: checkInputValue(ref.current!)
        }))
      }
    })
  }, [])

  // Also check when watched values change
  useEffect(() => {
    Object.entries(watchedFields).forEach(([key, value]) => {
      setFieldValues(prev => ({
        ...prev,
        [key]: !!value && value.length > 0
      }))
    })
  }, [watchedFields.fullName, watchedFields.companyName, watchedFields.email, watchedFields.password, watchedFields.confirmPassword])

  useEffect(() => {
    setMounted(true)
  }, [])

  const onSubmit = async (data: SignUpForm) => {
    setIsLoading(true)

    try {
      // Split full name into first and last name
      const nameParts = data.fullName.trim().split(' ')
      const firstName = nameParts[0]
      const lastName = nameParts.slice(1).join(' ') || ''

      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName,
          lastName,
          email: data.email,
          password: data.password,
          companyName: data.companyName,
          role: 'CLIENT'
        }),
      })

      const result = await response.json()

      if (response.ok) {
        toast.success('Account created successfully! Please sign in.')
        router.push('/client-auth/signin')
      } else {
        toast.error(result.error || 'Failed to create account. Please try again.')
      }
    } catch (error) {
      console.error('Sign-up error:', error)
      toast.error('An unexpected error occurred. Please try again later.')
    } finally {
      setIsLoading(false)
    }
  }

  if (!mounted) {
    return null
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 py-12 px-4 sm:px-6 lg:px-8 transition-colors duration-300">
      <Toaster position="top-right" />
      
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 dark:bg-grid-slate-700/25 bg-[size:20px_20px] opacity-60" />
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/50 to-transparent dark:via-slate-900/50" />
      
      {/* Theme Toggle */}
      <div className="absolute top-4 right-4 z-10">
        <div className="flex items-center space-x-2 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg p-1 border border-slate-200/50 dark:border-slate-700/50">
          <button
            onClick={() => setTheme('light')}
            className={`p-2 rounded-md transition-colors ${
              theme === 'light' 
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' 
                : 'text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200'
            }`}
          >
            <SunIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => setTheme('dark')}
            className={`p-2 rounded-md transition-colors ${
              theme === 'dark' 
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' 
                : 'text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200'
            }`}
          >
            <MoonIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => setTheme('system')}
            className={`p-2 rounded-md transition-colors ${
              theme === 'system' 
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' 
                : 'text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200'
            }`}
          >
            <ComputerDesktopIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative max-w-md w-full space-y-8">
        {/* Sign Up Card */}
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 dark:border-slate-700/50 p-8 space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
              <BuildingOfficeIcon className="h-8 w-8 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white">
              Create Account
            </h2>
            <p className="mt-2 text-sm text-slate-600 dark:text-slate-400">
              Join our client portal today
            </p>
          </div>

          {/* Form */}
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {/* Full Name Field */}
            <div className="space-y-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <UserIcon className="h-5 w-5 text-slate-400" />
                </div>
                <input
                  {...register('fullName', {
                    onChange: () => {
                      if (fieldRefs.fullName.current) {
                        setFieldValues(prev => ({
                          ...prev,
                          fullName: checkInputValue(fieldRefs.fullName.current!)
                        }))
                      }
                    }
                  })}
                  ref={(e) => {
                    fieldRefs.fullName.current = e
                    register('fullName').ref(e)
                  }}
                  type="text"
                  autoComplete="name"
                  className={`block w-full pl-10 pr-3 py-3 border rounded-xl shadow-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/50 dark:bg-slate-700/50 backdrop-blur-sm ${
                    errors.fullName 
                      ? 'border-red-300 dark:border-red-600' 
                      : 'border-slate-300 dark:border-slate-600'
                  }`}
                  placeholder="Enter your full name"
                />
                <label 
                  className={`absolute left-10 transition-all duration-200 pointer-events-none ${
                    fieldValues.fullName || watchedFields.fullName
                      ? '-top-2 text-xs bg-white dark:bg-slate-800 px-1 text-blue-600 dark:text-blue-400'
                      : 'top-3 text-sm text-slate-500 dark:text-slate-400'
                  }`}
                >
                  Full Name
                </label>
              </div>
              {errors.fullName && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.fullName.message}</p>
              )}
            </div>

            {/* Company Name Field */}
            <div className="space-y-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <BuildingOfficeIcon className="h-5 w-5 text-slate-400" />
                </div>
                <input
                  {...register('companyName', {
                    onChange: () => {
                      if (fieldRefs.companyName.current) {
                        setFieldValues(prev => ({
                          ...prev,
                          companyName: checkInputValue(fieldRefs.companyName.current!)
                        }))
                      }
                    }
                  })}
                  ref={(e) => {
                    fieldRefs.companyName.current = e
                    register('companyName').ref(e)
                  }}
                  type="text"
                  autoComplete="organization"
                  className={`block w-full pl-10 pr-3 py-3 border rounded-xl shadow-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/50 dark:bg-slate-700/50 backdrop-blur-sm ${
                    errors.companyName 
                      ? 'border-red-300 dark:border-red-600' 
                      : 'border-slate-300 dark:border-slate-600'
                  }`}
                  placeholder="Enter your company name"
                />
                <label 
                  className={`absolute left-10 transition-all duration-200 pointer-events-none ${
                    fieldValues.companyName || watchedFields.companyName
                      ? '-top-2 text-xs bg-white dark:bg-slate-800 px-1 text-blue-600 dark:text-blue-400'
                      : 'top-3 text-sm text-slate-500 dark:text-slate-400'
                  }`}
                >
                  Company Name
                </label>
              </div>
              {errors.companyName && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.companyName.message}</p>
              )}
            </div>

            {/* Email Field */}
            <div className="space-y-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <EnvelopeIcon className="h-5 w-5 text-slate-400" />
                </div>
                <input
                  {...register('email', {
                    onChange: () => {
                      if (fieldRefs.email.current) {
                        setFieldValues(prev => ({
                          ...prev,
                          email: checkInputValue(fieldRefs.email.current!)
                        }))
                      }
                    }
                  })}
                  ref={(e) => {
                    fieldRefs.email.current = e
                    register('email').ref(e)
                  }}
                  type="email"
                  autoComplete="email"
                  className={`block w-full pl-10 pr-3 py-3 border rounded-xl shadow-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/50 dark:bg-slate-700/50 backdrop-blur-sm ${
                    errors.email 
                      ? 'border-red-300 dark:border-red-600' 
                      : 'border-slate-300 dark:border-slate-600'
                  }`}
                  placeholder="Enter your email"
                />
                <label 
                  className={`absolute left-10 transition-all duration-200 pointer-events-none ${
                    fieldValues.email || watchedFields.email
                      ? '-top-2 text-xs bg-white dark:bg-slate-800 px-1 text-blue-600 dark:text-blue-400'
                      : 'top-3 text-sm text-slate-500 dark:text-slate-400'
                  }`}
                >
                  Email Address
                </label>
              </div>
              {errors.email && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.email.message}</p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LockClosedIcon className="h-5 w-5 text-slate-400" />
                </div>
                <input
                  {...register('password', {
                    onChange: () => {
                      if (fieldRefs.password.current) {
                        setFieldValues(prev => ({
                          ...prev,
                          password: checkInputValue(fieldRefs.password.current!)
                        }))
                      }
                    }
                  })}
                  ref={(e) => {
                    fieldRefs.password.current = e
                    register('password').ref(e)
                  }}
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  className={`block w-full pl-10 pr-10 py-3 border rounded-xl shadow-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/50 dark:bg-slate-700/50 backdrop-blur-sm ${
                    errors.password
                      ? 'border-red-300 dark:border-red-600'
                      : 'border-slate-300 dark:border-slate-600'
                  }`}
                  placeholder="Create a password"
                />
                <label
                  className={`absolute left-10 transition-all duration-200 pointer-events-none ${
                    fieldValues.password || watchedFields.password
                      ? '-top-2 text-xs bg-white dark:bg-slate-800 px-1 text-blue-600 dark:text-blue-400'
                      : 'top-3 text-sm text-slate-500 dark:text-slate-400'
                  }`}
                >
                  Password
                </label>
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors" />
                  ) : (
                    <EyeIcon className="h-5 w-5 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.password.message}</p>
              )}
            </div>

            {/* Confirm Password Field */}
            <div className="space-y-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LockClosedIcon className="h-5 w-5 text-slate-400" />
                </div>
                <input
                  {...register('confirmPassword', {
                    onChange: () => {
                      if (fieldRefs.confirmPassword.current) {
                        setFieldValues(prev => ({
                          ...prev,
                          confirmPassword: checkInputValue(fieldRefs.confirmPassword.current!)
                        }))
                      }
                    }
                  })}
                  ref={(e) => {
                    fieldRefs.confirmPassword.current = e
                    register('confirmPassword').ref(e)
                  }}
                  type={showConfirmPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  className={`block w-full pl-10 pr-10 py-3 border rounded-xl shadow-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/50 dark:bg-slate-700/50 backdrop-blur-sm ${
                    errors.confirmPassword
                      ? 'border-red-300 dark:border-red-600'
                      : 'border-slate-300 dark:border-slate-600'
                  }`}
                  placeholder="Confirm your password"
                />
                <label
                  className={`absolute left-10 transition-all duration-200 pointer-events-none ${
                    fieldValues.confirmPassword || watchedFields.confirmPassword
                      ? '-top-2 text-xs bg-white dark:bg-slate-800 px-1 text-blue-600 dark:text-blue-400'
                      : 'top-3 text-sm text-slate-500 dark:text-slate-400'
                  }`}
                >
                  Confirm Password
                </label>
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeSlashIcon className="h-5 w-5 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors" />
                  ) : (
                    <EyeIcon className="h-5 w-5 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.confirmPassword.message}</p>
              )}
            </div>

            {/* Terms and Privacy Policy */}
            <div className="space-y-1">
              <div className="flex items-start">
                <input
                  {...register('agreeToTerms')}
                  id="agree-terms"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 dark:border-slate-600 rounded bg-white/50 dark:bg-slate-700/50 mt-1"
                />
                <label htmlFor="agree-terms" className="ml-3 block text-sm text-slate-700 dark:text-slate-300">
                  I agree to the{' '}
                  <Link href="/terms" className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                    Terms of Service
                  </Link>
                  {' '}and{' '}
                  <Link href="/privacy" className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                    Privacy Policy
                  </Link>
                </label>
              </div>
              {errors.agreeToTerms && (
                <p className="text-sm text-red-600 dark:text-red-400">{errors.agreeToTerms.message}</p>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                {isLoading ? (
                  <svg className="animate-spin h-5 w-5 text-white/70" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <CheckCircleIcon className="h-5 w-5 text-white/70 group-hover:text-white transition-colors" />
                )}
              </span>
              {isLoading ? 'Creating Account...' : 'Create Account'}
            </button>
          </form>

          {/* Sign In Link */}
          <div className="text-center">
            <p className="text-sm text-slate-600 dark:text-slate-400">
              Already have an account?{' '}
              <Link
                href="/client-auth/signin"
                className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
              >
                Sign in here
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

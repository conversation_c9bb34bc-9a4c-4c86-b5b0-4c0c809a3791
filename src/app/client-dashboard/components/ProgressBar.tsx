interface ProgressBarProps {
  progress: number;
  color?: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export default function ProgressBar({ 
  progress, 
  color = '#4661e6', 
  showLabel = true,
  size = 'md' 
}: ProgressBarProps) {
  const getHeight = () => {
    switch (size) {
      case 'sm': return 'h-1';
      case 'md': return 'h-2';
      case 'lg': return 'h-3';
      default: return 'h-2';
    }
  };

  const clampedProgress = Math.min(Math.max(progress, 0), 100);

  return (
    <div className="w-full">
      {showLabel && (
        <div className="flex justify-between text-sm mb-1">
          <span className="text-[#647196]">Progress</span>
          <span className="text-[#3a4374] font-medium">{clampedProgress}%</span>
        </div>
      )}
      <div className={`w-full bg-[#f2f4ff] rounded-full ${getHeight()}`}>
        <div 
          className={`${getHeight()} rounded-full transition-all duration-500 ease-out`}
          style={{ 
            width: `${clampedProgress}%`,
            backgroundColor: color
          }}
        ></div>
      </div>
    </div>
  );
}

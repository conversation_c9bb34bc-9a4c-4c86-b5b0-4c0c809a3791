interface StatusBadgeProps {
  status: string;
  type?: 'project' | 'invoice' | 'payment';
}

export default function StatusBadge({ status, type = 'project' }: StatusBadgeProps) {
  const getStatusStyles = () => {
    const baseClasses = 'px-3 py-1 rounded-full text-sm font-medium';
    
    switch (status) {
      case 'in_progress':
        return `${baseClasses} bg-orange-100 text-orange-600`;
      case 'planning':
        return `${baseClasses} bg-purple-100 text-purple-600`;
      case 'completed':
        return `${baseClasses} bg-blue-100 text-blue-600`;
      case 'paid':
        return `${baseClasses} bg-green-100 text-green-600`;
      case 'pending':
        return `${baseClasses} bg-yellow-100 text-yellow-600`;
      case 'overdue':
        return `${baseClasses} bg-red-100 text-red-600`;
      case 'on_hold':
        return `${baseClasses} bg-gray-100 text-gray-600`;
      case 'cancelled':
        return `${baseClasses} bg-red-100 text-red-600`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-600`;
    }
  };

  const formatStatus = (status: string) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <span className={getStatusStyles()}>
      {formatStatus(status)}
    </span>
  );
}

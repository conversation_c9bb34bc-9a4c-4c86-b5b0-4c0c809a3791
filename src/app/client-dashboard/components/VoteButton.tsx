'use client';

import { useState } from 'react';
import { ArrowUpIcon } from '@heroicons/react/24/outline';

interface VoteButtonProps {
  initialVotes: number;
  isVoted?: boolean;
  onVote?: (votes: number) => void;
}

export default function VoteButton({ initialVotes, isVoted = false, onVote }: VoteButtonProps) {
  const [votes, setVotes] = useState(initialVotes);
  const [voted, setVoted] = useState(isVoted);
  const [isAnimating, setIsAnimating] = useState(false);

  const handleVote = () => {
    setIsAnimating(true);
    
    if (voted) {
      setVotes(votes - 1);
      setVoted(false);
    } else {
      setVotes(votes + 1);
      setVoted(true);
    }
    
    setTimeout(() => setIsAnimating(false), 200);
    
    if (onVote) {
      onVote(voted ? votes - 1 : votes + 1);
    }
  };

  return (
    <button
      onClick={handleVote}
      className={`
        bg-[#f2f4ff] rounded-lg p-3 text-center min-w-[60px] transition-all duration-200
        hover:bg-[#e8ecff] hover:shadow-md hover:-translate-y-0.5
        ${voted ? 'bg-[#4661e6] text-white' : ''}
        ${isAnimating ? 'scale-110' : ''}
      `}
    >
      <ArrowUpIcon 
        className={`w-4 h-4 mx-auto mb-1 transition-colors ${
          voted ? 'text-white' : 'text-[#4661e6]'
        }`} 
      />
      <span className={`text-sm font-bold transition-colors ${
        voted ? 'text-white' : 'text-[#3a4374]'
      }`}>
        {votes}
      </span>
    </button>
  );
}

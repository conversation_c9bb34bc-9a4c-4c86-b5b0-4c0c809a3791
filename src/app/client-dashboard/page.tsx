'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { toast, Toaster } from 'react-hot-toast'
import {
  BuildingOfficeIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  CreditCardIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  UserIcon,
  CogIcon,
  ChartBarIcon,
  FolderIcon,
  BanknotesIcon,
  ChatBubbleLeftRightIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ArrowTopRightOnSquareIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  TableCellsIcon,
  AdjustmentsHorizontalIcon,
  ChevronUpDownIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ArrowsUpDownIcon,
  EllipsisVerticalIcon,
  ShieldCheckIcon,
  KeyIcon,
  PhotoIcon,
  HomeIcon,
  Cog6ToothIcon,
  ChatBubbleLeftIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'
import PaymentModal from '@/components/client/payment-modal'
import QuotationModal from '@/components/client/quotation-modal'
import PasswordModal from '@/components/client/password-modal'
import ProfileModal from '@/components/client/profile-modal'
import { useServices } from '@/hooks/useServices'

// Type definitions
interface Client {
  id: string
  companyname: string
  contactname: string
  contactemail: string
  contactphone?: string
  address?: string
  city?: string
  state?: string
  zipcode?: string
  country?: string
  logourl?: string
  notes?: string
}

interface Project {
  id: string
  name: string
  description?: string
  status: string
  projstartdate?: string
  projcompletiondate?: string
  estimatecost?: number
  updatedat: string
}

interface Invoice {
  id: string
  description: string
  totalamount: number
  duedate: string
  status: string
  createdat: string
  updatedat: string
  payments?: Payment[]
}

interface Payment {
  id: string
  amount: number
  paymentmethod: string
  paymentdate: string
  status: string
  invoiceid: string
  updatedat: string
}

interface Message {
  id: string
  subject: string
  content: string
  isread: boolean
  createdat: string
}

interface QuoteRequest {
  id: string
  servicename: string
  budget: number
  timeline: string
  status: string
  createdat: string
}

// Dashboard sections configuration
const dashboardSections = [
  { id: 'overview', name: 'Overview', icon: HomeIcon },
  { id: 'projects', name: 'Projects', icon: FolderIcon },
  { id: 'invoices', name: 'Invoices', icon: DocumentTextIcon },
  { id: 'payments', name: 'Payments', icon: CreditCardIcon },
  { id: 'quotations', name: 'Quotations', icon: BanknotesIcon },
  { id: 'messages', name: 'Messages', icon: ChatBubbleLeftRightIcon },
  { id: 'settings', name: 'Settings', icon: Cog6ToothIcon },
]

export default function ClientDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { services } = useServices()
  const [activeSection, setActiveSection] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [client, setClient] = useState<Client | null>(null)
  const [projects, setProjects] = useState<Project[]>([])
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [payments, setPayments] = useState<Payment[]>([])
  const [messages, setMessages] = useState<Message[]>([])
  const [quotations, setQuotations] = useState<QuoteRequest[]>([])

  // Modal states
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [showQuotationModal, setShowQuotationModal] = useState(false)
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [showProfileModal, setShowProfileModal] = useState(false)

  // Utility functions
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'completed':
      case 'paid':
        return 'text-green-600 bg-green-100'
      case 'pending':
      case 'in_progress':
        return 'text-yellow-600 bg-yellow-100'
      case 'overdue':
      case 'cancelled':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  // Data fetching functions
  const fetchClientData = async () => {
    try {
      const response = await fetch('/api/client/dashboard')
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data) {
          setClient(data.data)
          setProjects(data.data.projects || [])
          setInvoices(data.data.invoices || [])

          // Extract payments from all invoices
          const allPayments = (data.data.invoices || []).flatMap((invoice: any) =>
            (invoice.payments || []).map((payment: any) => ({
              ...payment,
              invoiceId: invoice.id
            }))
          )
          setPayments(allPayments)
        } else {
          toast.error('No client account linked to your user account')
        }
      } else {
        toast.error('Failed to load client data')
      }
    } catch (error) {
      console.error('Error fetching client data:', error)
      toast.error('Failed to load client data')
    }
  }

  const fetchMessages = async () => {
    try {
      const response = await fetch(`/api/clients/${client?.id}/messages`)
      if (response.ok) {
        const data = await response.json()
        setMessages(data)
      }
    } catch (error) {
      console.error('Error fetching messages:', error)
    }
  }

  useEffect(() => {
    if (status === 'loading') return

    if (status === 'unauthenticated') {
      router.push('/client-auth/signin')
      return
    }

    if (session?.user?.role !== 'CLIENT') {
      router.push('/')
      return
    }

    const loadData = async () => {
      setLoading(true)
      await Promise.all([fetchClientData(), fetchMessages()])
      setLoading(false)
    }
    loadData()
  }, [session, status, router])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ArrowPathIcon className="mx-auto h-12 w-12 text-gray-400 animate-spin" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Loading...</h3>
          <p className="mt-1 text-sm text-gray-500">Please wait while we load your dashboard.</p>
        </div>
      </div>
    )
  }

  // Render functions
  const renderOverview = () => {
    const totalInvoiceAmount = invoices.reduce((sum, invoice) => sum + invoice.totalamount, 0)
    const paidAmount = payments.reduce((sum, payment) => sum + payment.amount, 0)
    const pendingAmount = totalInvoiceAmount - paidAmount
    const activeProjects = projects.filter(p => p.status === 'active' || p.status === 'in_progress').length

    return (
      <div className="space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FolderIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active Projects</dt>
                    <dd className="text-lg font-medium text-gray-900">{activeProjects}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentTextIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Invoices</dt>
                    <dd className="text-lg font-medium text-gray-900">{invoices.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CreditCardIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Amount Paid</dt>
                    <dd className="text-lg font-medium text-gray-900">{formatCurrency(paidAmount)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Pending Amount</dt>
                    <dd className="text-lg font-medium text-gray-900">{formatCurrency(pendingAmount)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
            <div className="space-y-4">
              {projects.slice(0, 3).map((project) => (
                <div key={project.id} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <FolderIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{project.name}</p>
                    <p className="text-sm text-gray-500 truncate">{project.description}</p>
                  </div>
                  <div className="flex-shrink-0">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                      {project.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  const renderProjects = () => (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Your Projects</h3>
            <button
              onClick={() => setShowQuotationModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Request Quote
            </button>
          </div>
          <div className="space-y-4">
            {projects.map((project) => (
              <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-gray-900">{project.name}</h4>
                    <p className="text-sm text-gray-500 mt-1">{project.description}</p>
                    {project.projstartdate && (
                      <p className="text-xs text-gray-400 mt-2">
                        Started: {formatDate(project.projstartdate)}
                        {project.projcompletiondate && ` • Completed: ${formatDate(project.projcompletiondate)}`}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center space-x-3">
                    {project.estimatecost && (
                      <span className="text-sm font-medium text-gray-900">
                        {formatCurrency(project.estimatecost)}
                      </span>
                    )}
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                      {project.status}
                    </span>
                  </div>
                </div>
              </div>
            ))}
            {projects.length === 0 && (
              <div className="text-center py-8">
                <FolderIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No projects</h3>
                <p className="mt-1 text-sm text-gray-500">You don't have any projects yet.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )

  const renderInvoices = () => (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Invoices</h3>
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Invoice
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Due Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {invoices.map((invoice) => (
                  <tr key={invoice.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      #{invoice.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(invoice.totalamount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(invoice.duedate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                        {invoice.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900 mr-3">
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      {invoice.status === 'pending' && (
                        <button
                          onClick={() => setShowPaymentModal(true)}
                          className="text-green-600 hover:text-green-900"
                        >
                          Pay Now
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {invoices.length === 0 && (
              <div className="text-center py-8">
                <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices</h3>
                <p className="mt-1 text-sm text-gray-500">You don't have any invoices yet.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )

  const renderPayments = () => (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Payment History</h3>
            <button
              onClick={() => setShowPaymentModal(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Payment
            </button>
          </div>
          <div className="overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Invoice
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {payments.map((payment) => (
                  <tr key={payment.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(payment.paymentdate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatCurrency(payment.amount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {payment.paymentmethod}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      #{payment.invoiceid}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                        {payment.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {payments.length === 0 && (
              <div className="text-center py-8">
                <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No payments</h3>
                <p className="mt-1 text-sm text-gray-500">You haven't made any payments yet.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )

  const renderMessages = () => (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Messages</h3>
          <div className="text-center py-8">
            <ChatBubbleLeftIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No messages</h3>
            <p className="mt-1 text-sm text-gray-500">Your message history will appear here.</p>
          </div>
        </div>
      </div>
    </div>
  )

  const renderSettings = () => (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Account Settings</h3>
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Profile Information</label>
              <div className="mt-1">
                <button
                  onClick={() => setShowProfileModal(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
                >
                  Edit Profile
                </button>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Password</label>
              <div className="mt-1">
                <button
                  onClick={() => setShowPasswordModal(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
                >
                  Change Password
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <Toaster position="top-right" />
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Client Dashboard</h1>
                <p className="mt-1 text-sm text-gray-500">
                  Welcome back, {client?.contactname || 'Client'}
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <BuildingOfficeIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-900">{client?.companyname}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-900">{client?.contactemail}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar Navigation */}
            <div className="lg:w-64 flex-shrink-0">
              <nav className="space-y-1">
                {dashboardSections.map((section) => {
                  const Icon = section.icon
                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={`w-full group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        activeSection === section.id
                          ? 'bg-blue-100 text-blue-700 border-blue-300'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <Icon
                        className={`flex-shrink-0 -ml-1 mr-3 h-5 w-5 ${
                          activeSection === section.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                        }`}
                      />
                      <span className="truncate">{section.name}</span>
                    </button>
                  )
                })}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1 min-w-0">
              <motion.div
                key={activeSection}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                {activeSection === 'overview' && renderOverview()}
                {activeSection === 'projects' && renderProjects()}
                {activeSection === 'invoices' && renderInvoices()}
                {activeSection === 'payments' && renderPayments()}
                {activeSection === 'messages' && renderMessages()}
                {activeSection === 'settings' && renderSettings()}
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal Components */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        invoices={invoices}
        onSubmit={async (paymentData) => {
          try {
            const response = await fetch('/api/payments', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                ...paymentData,
                clientId: client?.id
              })
            })

            if (response.ok) {
              toast.success('Payment submitted successfully!')
              fetchClientData() // Refresh data
            } else {
              throw new Error('Payment failed')
            }
          } catch (error) {
            console.error('Payment error:', error)
            toast.error('Failed to process payment')
            throw error
          }
        }}
      />

      <QuotationModal
        isOpen={showQuotationModal}
        onClose={() => setShowQuotationModal(false)}
        services={services}
        onSubmit={async (quotationData) => {
          try {
            const response = await fetch('/api/quotations', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                ...quotationData,
                clientId: client?.id
              })
            })

            if (response.ok) {
              toast.success('Quotation request submitted successfully!')
              // Refresh quotations data if needed
            } else {
              throw new Error('Quotation submission failed')
            }
          } catch (error) {
            console.error('Quotation error:', error)
            toast.error('Failed to submit quotation request')
            throw error
          }
        }}
      />

      <PasswordModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        onSubmit={async (passwordData) => {
          try {
            const response = await fetch('/api/auth/change-password', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(passwordData)
            })

            if (response.ok) {
              toast.success('Password changed successfully! Please sign in again.')
              // Sign out user after password change
              setTimeout(() => {
                window.location.href = '/client-auth/signin'
              }, 2000)
            } else {
              const error = await response.json()
              throw new Error(error.message || 'Password change failed')
            }
          } catch (error) {
            console.error('Password change error:', error)
            toast.error(error.message || 'Failed to change password')
            throw error
          }
        }}
      />

      <ProfileModal
        isOpen={showProfileModal}
        onClose={() => setShowProfileModal(false)}
        client={client}
        onSubmit={async (profileData) => {
          try {
            const response = await fetch(`/api/clients/${client?.id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(profileData)
            })

            if (response.ok) {
              toast.success('Profile updated successfully!')
              fetchClientData() // Refresh client data
            } else {
              throw new Error('Profile update failed')
            }
          } catch (error) {
            console.error('Profile update error:', error)
            toast.error('Failed to update profile')
            throw error
          }
        }}
      />
    </div>
  )
}

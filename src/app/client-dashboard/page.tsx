'use client';

import { useState } from 'react';
import {
  HomeIcon,
  FolderIcon,
  DocumentTextIcon,
  CreditCardIcon,
  ClockIcon,
  ChatBubbleLeftIcon,
  Cog6ToothIcon,
  UserCircleIcon,
  ChevronDownIcon,
  BellIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  ArrowUpIcon
} from '@heroicons/react/24/outline';
import VoteButton from './components/VoteButton';
import StatusBadge from './components/StatusBadge';
import ProgressBar from './components/ProgressBar';

export default function ClientDashboard() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);

  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: HomeIcon },
    { id: 'projects', label: 'Projects', icon: FolderIcon },
    { id: 'invoices', label: 'Invoices', icon: DocumentTextIcon },
    { id: 'payments', label: 'Payments', icon: CreditCardIcon },
    { id: 'history', label: 'Payment History', icon: ClockIcon },
    { id: 'quote', label: 'Request Quote', icon: PlusIcon },
    { id: 'messages', label: 'Messages', icon: ChatBubbleLeftIcon },
    { id: 'settings', label: 'Settings', icon: Cog6ToothIcon },
  ];

  const projects = [
    {
      id: 1,
      title: 'E-commerce Website Development',
      description: 'Complete online store with payment integration and admin panel',
      status: 'in_progress',
      progress: 75,
      votes: 112,
      comments: 2,
      category: 'enhancement'
    },
    {
      id: 2,
      title: 'Mobile App Development',
      description: 'Cross-platform mobile application for iOS and Android',
      status: 'planning',
      progress: 25,
      votes: 99,
      comments: 4,
      category: 'feature'
    },
    {
      id: 3,
      title: 'Website Maintenance',
      description: 'Monthly maintenance and security updates',
      status: 'completed',
      progress: 100,
      votes: 65,
      comments: 1,
      category: 'bug'
    }
  ];

  const invoices = [
    {
      id: 'INV-001',
      amount: 5500,
      status: 'paid',
      dueDate: '2024-02-15',
      description: 'Website Development - Phase 1'
    },
    {
      id: 'INV-002', 
      amount: 3200,
      status: 'pending',
      dueDate: '2024-03-01',
      description: 'Mobile App Development - Initial Setup'
    },
    {
      id: 'INV-003',
      amount: 800,
      status: 'overdue',
      dueDate: '2024-01-30',
      description: 'Monthly Maintenance - January'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_progress': return 'bg-orange-100 text-orange-600';
      case 'planning': return 'bg-purple-100 text-purple-600';
      case 'completed': return 'bg-blue-100 text-blue-600';
      case 'paid': return 'bg-green-100 text-green-600';
      case 'pending': return 'bg-yellow-100 text-yellow-600';
      case 'overdue': return 'bg-red-100 text-red-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'enhancement': return 'bg-blue-50 text-blue-600';
      case 'feature': return 'bg-purple-50 text-purple-600';
      case 'bug': return 'bg-orange-50 text-orange-600';
      default: return 'bg-gray-50 text-gray-600';
    }
  };

  return (
    <div className="min-h-screen bg-[#f7f8fd] font-['Jost']">
      {/* Header */}
      <header className="bg-white border-b border-[#e1e5f2] px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="text-2xl font-bold bg-gradient-to-r from-[#667eea] to-[#764ba2] bg-clip-text text-transparent">
              TechnoloWay
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <button className="p-2 text-[#647196] hover:text-[#3a4374] transition-colors">
              <BellIcon className="w-6 h-6" />
            </button>
            
            <div className="relative">
              <button 
                onClick={() => setShowProfileDropdown(!showProfileDropdown)}
                className="flex items-center space-x-2 p-2 rounded-lg hover:bg-[#f2f4ff] transition-colors"
              >
                <div className="w-8 h-8 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">JD</span>
                </div>
                <span className="text-[#3a4374] font-medium">John Doe</span>
                <ChevronDownIcon className="w-4 h-4 text-[#647196]" />
              </button>
              
              {showProfileDropdown && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-[#e1e5f2] py-2 z-50">
                  <a href="#" className="block px-4 py-2 text-[#3a4374] hover:bg-[#f2f4ff]">Profile</a>
                  <a href="#" className="block px-4 py-2 text-[#3a4374] hover:bg-[#f2f4ff]">Settings</a>
                  <hr className="my-2 border-[#e1e5f2]" />
                  <a href="#" className="block px-4 py-2 text-[#3a4374] hover:bg-[#f2f4ff]">Sign Out</a>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-80 bg-white border-r border-[#e1e5f2] min-h-screen p-6">
          {/* Client Info Panel */}
          <div className="bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-xl p-6 text-white mb-6">
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <span className="text-2xl font-bold">JD</span>
              </div>
              <div>
                <h3 className="text-xl font-bold">John Doe</h3>
                <p className="text-white/90 text-sm">Tech Solutions Inc</p>
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <p className="text-white/90"><EMAIL></p>
              <p className="text-white/90">+****************</p>
            </div>
          </div>

          {/* Navigation */}
          <nav className="space-y-2">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                    activeTab === item.id
                      ? 'bg-[#4661e6] text-white'
                      : 'text-[#647196] hover:bg-[#f2f4ff] hover:text-[#3a4374]'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{item.label}</span>
                </button>
              );
            })}
          </nav>

          {/* Project Status Sidebar */}
          <div className="mt-8 bg-white rounded-lg border border-[#e1e5f2] p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-semibold text-[#3a4374]">Project Status</h4>
              <button className="text-[#4661e6] text-sm font-medium">View Details</button>
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-[#f49f85] rounded-full"></div>
                  <span className="text-sm text-[#647196]">In Progress</span>
                </div>
                <span className="text-sm font-medium text-[#3a4374]">3</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-[#ad1fea] rounded-full"></div>
                  <span className="text-sm text-[#647196]">Under Review</span>
                </div>
                <span className="text-sm font-medium text-[#3a4374]">2</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-[#4661e6] rounded-full"></div>
                  <span className="text-sm text-[#647196]">Completed</span>
                </div>
                <span className="text-sm font-medium text-[#3a4374]">1</span>
              </div>
            </div>
          </div>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              {/* Header */}
              <div className="bg-[#3a4374] rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-8 h-8 text-yellow-400">
                      <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                      </svg>
                    </div>
                    <div>
                      <h1 className="text-xl font-semibold">6 Active Items</h1>
                      <p className="text-white/80 text-sm">Sort by: Most Recent</p>
                    </div>
                  </div>
                  <button className="bg-gradient-to-r from-[#f093fb] to-[#f5576c] px-6 py-2 rounded-lg font-medium hover:shadow-lg transition-shadow">
                    + Add New
                  </button>
                </div>
              </div>

              {/* Navigation Tabs */}
              <div className="bg-white rounded-lg p-2 border border-[#e1e5f2]">
                <div className="flex space-x-2">
                  <button className="bg-[#4661e6] text-white px-4 py-2 rounded-lg text-sm font-medium">
                    All
                  </button>
                  <button className="text-[#647196] px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#f2f4ff]">
                    Projects
                  </button>
                  <button className="text-[#647196] px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#f2f4ff]">
                    Invoices
                  </button>
                  <button className="text-[#647196] px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#f2f4ff]">
                    Payments
                  </button>
                </div>
              </div>

              {/* Filter Tags */}
              <div className="flex space-x-3">
                <span className="bg-[#4661e6] text-white px-3 py-1 rounded-full text-sm font-medium">
                  Active Projects
                </span>
                <span className="bg-[#ad1fea] text-white px-3 py-1 rounded-full text-sm font-medium">
                  Pending Invoices
                </span>
                <span className="bg-[#f49f85] text-white px-3 py-1 rounded-full text-sm font-medium">
                  Completed
                </span>
              </div>

              {/* Projects List */}
              <div className="space-y-4">
                {projects.map((project) => (
                  <div key={project.id} className="bg-white rounded-xl p-6 border border-[#e1e5f2] hover:shadow-lg hover:-translate-y-0.5 transition-all cursor-pointer">
                    <div className="flex items-start space-x-4">
                      {/* Vote Section */}
                      <VoteButton
                        initialVotes={project.votes}
                        onVote={(votes) => console.log(`Project ${project.id} now has ${votes} votes`)}
                      />

                      {/* Content */}
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-[#3a4374] mb-2 hover:text-[#4661e6] transition-colors">
                          {project.title}
                        </h3>
                        <p className="text-[#647196] mb-4 leading-relaxed">
                          {project.description}
                        </p>

                        {/* Progress Bar */}
                        <div className="mb-4">
                          <ProgressBar progress={project.progress} />
                        </div>

                        <div className="flex items-center justify-between">
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(project.category)}`}>
                            {project.category}
                          </span>
                          <StatusBadge status={project.status} type="project" />
                        </div>
                      </div>

                      {/* Comments */}
                      <div className="flex items-center space-x-1 text-[#8c92b3]">
                        <ChatBubbleLeftIcon className="w-4 h-4" />
                        <span className="text-sm font-medium text-[#3a4374]">{project.comments}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'invoices' && (
            <div className="space-y-6">
              <div className="bg-[#3a4374] rounded-xl p-6 text-white">
                <h1 className="text-2xl font-bold">Invoices</h1>
                <p className="text-white/80">Manage your invoices and payments</p>
              </div>

              <div className="grid gap-4">
                {invoices.map((invoice) => (
                  <div key={invoice.id} className="bg-white rounded-xl p-6 border border-[#e1e5f2] hover:shadow-lg transition-shadow">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 mb-2">
                          <h3 className="text-lg font-semibold text-[#3a4374]">{invoice.id}</h3>
                          <StatusBadge status={invoice.status} type="invoice" />
                        </div>
                        <p className="text-[#647196] mb-2">{invoice.description}</p>
                        <p className="text-sm text-[#8c92b3]">Due: {invoice.dueDate}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-[#3a4374]">${invoice.amount.toLocaleString()}</p>
                        <button className="mt-2 bg-[#4661e6] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#3651d4] transition-colors">
                          {invoice.status === 'paid' ? 'View Receipt' : 'Pay Now'}
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'payments' && (
            <div className="space-y-6">
              <div className="bg-[#3a4374] rounded-xl p-6 text-white">
                <h1 className="text-2xl font-bold">Make Payment</h1>
                <p className="text-white/80">Secure payment processing</p>
              </div>

              <div className="bg-white rounded-xl p-6 border border-[#e1e5f2]">
                <h2 className="text-xl font-semibold text-[#3a4374] mb-6">Payment Details</h2>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-[#3a4374] mb-2">Select Invoice</label>
                    <select className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent">
                      <option>INV-002 - $3,200 (Due: 2024-03-01)</option>
                      <option>INV-003 - $800 (Overdue)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#3a4374] mb-2">Payment Method</label>
                    <select className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent">
                      <option>💳 Credit Card</option>
                      <option>🏦 Bank Transfer</option>
                      <option>💰 PayPal</option>
                    </select>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-[#f2f4ff] rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-[#647196]">Total Amount:</span>
                    <span className="text-2xl font-bold text-[#3a4374]">$3,200.00</span>
                  </div>
                </div>

                <button className="w-full mt-6 bg-gradient-to-r from-[#f093fb] to-[#f5576c] text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-shadow">
                  Process Payment
                </button>
              </div>
            </div>
          )}

          {activeTab === 'history' && (
            <div className="space-y-6">
              <div className="bg-[#3a4374] rounded-xl p-6 text-white">
                <h1 className="text-2xl font-bold">Payment History</h1>
                <p className="text-white/80">View all your past transactions</p>
              </div>

              <div className="bg-white rounded-xl border border-[#e1e5f2] overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-[#f2f4ff]">
                      <tr>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-[#3a4374]">Date</th>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-[#3a4374]">Invoice</th>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-[#3a4374]">Amount</th>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-[#3a4374]">Method</th>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-[#3a4374]">Status</th>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-[#3a4374]">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-[#e1e5f2]">
                      <tr className="hover:bg-[#f2f4ff] transition-colors">
                        <td className="px-6 py-4 text-sm text-[#647196]">2024-02-10</td>
                        <td className="px-6 py-4 text-sm font-medium text-[#3a4374]">INV-001</td>
                        <td className="px-6 py-4 text-sm font-semibold text-[#3a4374]">$5,500</td>
                        <td className="px-6 py-4 text-sm text-[#647196]">Credit Card</td>
                        <td className="px-6 py-4">
                          <span className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium">
                            Completed
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <button className="text-[#4661e6] text-sm font-medium hover:underline">
                            View Receipt
                          </button>
                        </td>
                      </tr>
                      <tr className="hover:bg-[#f2f4ff] transition-colors">
                        <td className="px-6 py-4 text-sm text-[#647196]">2024-01-15</td>
                        <td className="px-6 py-4 text-sm font-medium text-[#3a4374]">INV-000</td>
                        <td className="px-6 py-4 text-sm font-semibold text-[#3a4374]">$2,800</td>
                        <td className="px-6 py-4 text-sm text-[#647196]">Bank Transfer</td>
                        <td className="px-6 py-4">
                          <span className="bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs font-medium">
                            Completed
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <button className="text-[#4661e6] text-sm font-medium hover:underline">
                            View Receipt
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'quote' && (
            <div className="space-y-6">
              <div className="bg-[#3a4374] rounded-xl p-6 text-white">
                <h1 className="text-2xl font-bold">Request Quote</h1>
                <p className="text-white/80">Get a quote for your new project</p>
              </div>

              <div className="bg-white rounded-xl p-6 border border-[#e1e5f2]">
                <form className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-[#3a4374] mb-2">Project Type</label>
                      <select className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent">
                        <option>Website Development</option>
                        <option>Mobile App Development</option>
                        <option>E-commerce Solution</option>
                        <option>Custom Software</option>
                        <option>Maintenance & Support</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-[#3a4374] mb-2">Budget Range</label>
                      <select className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent">
                        <option>$1,000 - $5,000</option>
                        <option>$5,000 - $10,000</option>
                        <option>$10,000 - $25,000</option>
                        <option>$25,000+</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#3a4374] mb-2">Project Title</label>
                    <input
                      type="text"
                      className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent"
                      placeholder="Enter your project title"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#3a4374] mb-2">Project Description</label>
                    <textarea
                      rows={6}
                      className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent"
                      placeholder="Describe your project requirements, goals, and any specific features you need..."
                    ></textarea>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[#3a4374] mb-2">Timeline</label>
                    <select className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent">
                      <option>ASAP</option>
                      <option>1-2 weeks</option>
                      <option>1 month</option>
                      <option>2-3 months</option>
                      <option>3+ months</option>
                      <option>Flexible</option>
                    </select>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-[#f093fb] to-[#f5576c] text-white py-3 rounded-lg font-semibold hover:shadow-lg transition-shadow"
                  >
                    Submit Quote Request
                  </button>
                </form>
              </div>
            </div>
          )}

          {activeTab === 'messages' && (
            <div className="space-y-6">
              <div className="bg-[#3a4374] rounded-xl p-6 text-white">
                <h1 className="text-2xl font-bold">Messages</h1>
                <p className="text-white/80">Communicate with your project team</p>
              </div>

              <div className="grid lg:grid-cols-3 gap-6">
                {/* Message List */}
                <div className="lg:col-span-1">
                  <div className="bg-white rounded-xl border border-[#e1e5f2] h-96 overflow-hidden">
                    <div className="p-4 border-b border-[#e1e5f2]">
                      <h3 className="font-semibold text-[#3a4374]">Conversations</h3>
                    </div>
                    <div className="divide-y divide-[#e1e5f2]">
                      <div className="p-4 hover:bg-[#f2f4ff] cursor-pointer bg-[#f2f4ff]">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-medium">PM</span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-[#3a4374] truncate">Project Manager</p>
                            <p className="text-xs text-[#647196] truncate">Latest update on your website...</p>
                          </div>
                          <div className="w-2 h-2 bg-[#4661e6] rounded-full"></div>
                        </div>
                      </div>
                      <div className="p-4 hover:bg-[#f2f4ff] cursor-pointer">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-[#f093fb] to-[#f5576c] rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-medium">DS</span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-[#3a4374] truncate">Design Team</p>
                            <p className="text-xs text-[#647196] truncate">Design mockups are ready...</p>
                          </div>
                        </div>
                      </div>
                      <div className="p-4 hover:bg-[#f2f4ff] cursor-pointer">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center">
                            <span className="text-white text-sm font-medium">SP</span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-[#3a4374] truncate">Support Team</p>
                            <p className="text-xs text-[#647196] truncate">Your ticket has been resolved</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Chat Area */}
                <div className="lg:col-span-2">
                  <div className="bg-white rounded-xl border border-[#e1e5f2] h-96 flex flex-col">
                    {/* Chat Header */}
                    <div className="p-4 border-b border-[#e1e5f2]">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center">
                          <span className="text-white text-sm font-medium">PM</span>
                        </div>
                        <div>
                          <p className="font-semibold text-[#3a4374]">Project Manager</p>
                          <p className="text-xs text-[#647196]">Online</p>
                        </div>
                      </div>
                    </div>

                    {/* Messages */}
                    <div className="flex-1 p-4 space-y-4 overflow-y-auto">
                      <div className="flex space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-white text-xs font-medium">PM</span>
                        </div>
                        <div className="bg-[#f2f4ff] rounded-lg p-3 max-w-xs">
                          <p className="text-sm text-[#3a4374]">Hi John! I wanted to update you on the progress of your website development project.</p>
                          <p className="text-xs text-[#8c92b3] mt-1">2:30 PM</p>
                        </div>
                      </div>

                      <div className="flex space-x-3 justify-end">
                        <div className="bg-[#4661e6] rounded-lg p-3 max-w-xs">
                          <p className="text-sm text-white">That's great! How are we doing with the timeline?</p>
                          <p className="text-xs text-white/70 mt-1">2:32 PM</p>
                        </div>
                        <div className="w-8 h-8 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-white text-xs font-medium">JD</span>
                        </div>
                      </div>

                      <div className="flex space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-[#667eea] to-[#764ba2] rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-white text-xs font-medium">PM</span>
                        </div>
                        <div className="bg-[#f2f4ff] rounded-lg p-3 max-w-xs">
                          <p className="text-sm text-[#3a4374]">We're actually ahead of schedule! The design phase is complete and we're moving into development.</p>
                          <p className="text-xs text-[#8c92b3] mt-1">2:35 PM</p>
                        </div>
                      </div>
                    </div>

                    {/* Message Input */}
                    <div className="p-4 border-t border-[#e1e5f2]">
                      <div className="flex space-x-3">
                        <input
                          type="text"
                          placeholder="Type your message..."
                          className="flex-1 p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent"
                        />
                        <button className="bg-[#4661e6] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#3651d4] transition-colors">
                          Send
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="space-y-6">
              <div className="bg-[#3a4374] rounded-xl p-6 text-white">
                <h1 className="text-2xl font-bold">Settings</h1>
                <p className="text-white/80">Manage your account preferences</p>
              </div>

              <div className="grid gap-6">
                {/* Profile Settings */}
                <div className="bg-white rounded-xl p-6 border border-[#e1e5f2]">
                  <h2 className="text-xl font-semibold text-[#3a4374] mb-6">Profile Information</h2>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-[#3a4374] mb-2">Full Name</label>
                      <input
                        type="text"
                        value="John Doe"
                        className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#3a4374] mb-2">Email</label>
                      <input
                        type="email"
                        value="<EMAIL>"
                        className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#3a4374] mb-2">Company</label>
                      <input
                        type="text"
                        value="Tech Solutions Inc"
                        className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#3a4374] mb-2">Phone</label>
                      <input
                        type="tel"
                        value="+****************"
                        className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent"
                      />
                    </div>
                  </div>
                  <button className="mt-6 bg-[#4661e6] text-white px-6 py-2 rounded-lg font-medium hover:bg-[#3651d4] transition-colors">
                    Update Profile
                  </button>
                </div>

                {/* Password Settings */}
                <div className="bg-white rounded-xl p-6 border border-[#e1e5f2]">
                  <h2 className="text-xl font-semibold text-[#3a4374] mb-6">Change Password</h2>
                  <div className="space-y-4 max-w-md">
                    <div>
                      <label className="block text-sm font-medium text-[#3a4374] mb-2">Current Password</label>
                      <input
                        type="password"
                        className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#3a4374] mb-2">New Password</label>
                      <input
                        type="password"
                        className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#3a4374] mb-2">Confirm New Password</label>
                      <input
                        type="password"
                        className="w-full p-3 border border-[#e1e5f2] rounded-lg focus:ring-2 focus:ring-[#4661e6] focus:border-transparent"
                      />
                    </div>
                  </div>
                  <button className="mt-6 bg-[#4661e6] text-white px-6 py-2 rounded-lg font-medium hover:bg-[#3651d4] transition-colors">
                    Change Password
                  </button>
                </div>

                {/* Notification Settings */}
                <div className="bg-white rounded-xl p-6 border border-[#e1e5f2]">
                  <h2 className="text-xl font-semibold text-[#3a4374] mb-6">Notification Preferences</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-[#3a4374]">Email Notifications</p>
                        <p className="text-sm text-[#647196]">Receive updates about your projects via email</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" className="sr-only peer" defaultChecked />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#4661e6]"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-[#3a4374]">SMS Notifications</p>
                        <p className="text-sm text-[#647196]">Get text messages for urgent updates</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" className="sr-only peer" />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#4661e6]"></div>
                      </label>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-[#3a4374]">Invoice Reminders</p>
                        <p className="text-sm text-[#647196]">Reminders for upcoming payment due dates</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" className="sr-only peer" defaultChecked />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#4661e6]"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}

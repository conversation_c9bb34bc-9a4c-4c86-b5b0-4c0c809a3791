'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { toast, Toaster } from 'react-hot-toast'
import {
  BuildingOfficeIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  CreditCardIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  UserIcon,
  CogIcon,
  ChartBarIcon,
  FolderIcon,
  BanknotesIcon,
  ChatBubbleLeftRightIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ListBulletIcon,
  Squares2X2Icon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ArrowTopRightOnSquareIcon,
  XMarkIcon,
  TableCellsIcon,
  ChevronUpDownIcon,
  ArrowsUpDownIcon,
  EllipsisVerticalIcon,
  ShieldCheckIcon,
  KeyIcon,
  PhotoIcon,
  HomeIcon,
  Cog6ToothIcon,
  ChatBubbleLeftIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline'
import PaymentModal from '@/components/client/payment-modal'
import QuotationModal from '@/components/client/quotation-modal'
import PasswordModal from '@/components/client/password-modal'
import ProfileModal from '@/components/client/profile-modal'
import { useServices } from '@/hooks/useServices'

// Type definitions
interface Client {
  id: string
  companyname: string
  contactname: string
  contactemail: string
  contactphone?: string
  address?: string
  city?: string
  state?: string
  zipcode?: string
  country?: string
  logourl?: string
  notes?: string
}

interface Project {
  id: string
  name: string
  description?: string
  status: string
  projstartdate?: string
  projcompletiondate?: string
  estimatecost?: number
  updatedat: string
}

interface Invoice {
  id: string
  description: string
  totalamount: number
  duedate: string
  status: string
  createdat: string
  updatedat: string
  payments?: Payment[]
}

interface Payment {
  id: string
  amount: number
  paymentmethod: string
  paymentdate: string
  status: string
  invoiceid: string
  updatedat: string
}

interface Message {
  id: string
  subject: string
  content: string
  isread: boolean
  createdat: string
}

interface QuoteRequest {
  id: string
  servicename: string
  budget: number
  timeline: string
  status: string
  createdat: string
}

// Dashboard sections configuration
const dashboardSections = [
  { id: 'overview', name: 'Overview', icon: HomeIcon },
  { id: 'projects', name: 'Projects', icon: FolderIcon },
  { id: 'invoices', name: 'Invoices', icon: DocumentTextIcon },
  { id: 'payments', name: 'Payments', icon: CreditCardIcon },
  { id: 'quotations', name: 'Quotations', icon: BanknotesIcon },
  { id: 'messages', name: 'Messages', icon: ChatBubbleLeftRightIcon },
  { id: 'settings', name: 'Settings', icon: Cog6ToothIcon },
]

export default function ClientDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { services } = useServices()
  const [activeSection, setActiveSection] = useState('overview')
  const [loading, setLoading] = useState(true)
  const [client, setClient] = useState<Client | null>(null)
  const [projects, setProjects] = useState<Project[]>([])
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [payments, setPayments] = useState<Payment[]>([])
  const [messages, setMessages] = useState<Message[]>([])
  const [quotations, setQuotations] = useState<QuoteRequest[]>([])

  // Modal states
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [showQuotationModal, setShowQuotationModal] = useState(false)
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [showProfileModal, setShowProfileModal] = useState(false)

  // Utility functions
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'completed':
      case 'paid':
        return 'text-green-600 bg-green-100'
      case 'pending':
      case 'in_progress':
        return 'text-yellow-600 bg-yellow-100'
      case 'overdue':
      case 'cancelled':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  // Data fetching functions
  const fetchClientData = async () => {
    try {
      const response = await fetch('/api/client/dashboard')
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data) {
          setClient(data.data)
          setProjects(data.data.projects || [])
          setInvoices(data.data.invoices || [])

          // Extract payments from all invoices
          const allPayments = (data.data.invoices || []).flatMap((invoice: any) =>
            (invoice.payments || []).map((payment: any) => ({
              ...payment,
              invoiceId: invoice.id
            }))
          )
          setPayments(allPayments)
        } else {
          toast.error('No client account linked to your user account')
        }
      } else {
        toast.error('Failed to load client data')
      }
    } catch (error) {
      console.error('Error fetching client data:', error)
      toast.error('Failed to load client data')
    }
  }

  const fetchMessages = async () => {
    try {
      const response = await fetch(`/api/clients/${client?.id}/messages`)
      if (response.ok) {
        const data = await response.json()
        setMessages(data)
      }
    } catch (error) {
      console.error('Error fetching messages:', error)
    }
  }

  useEffect(() => {
    if (status === 'loading') return

    if (status === 'unauthenticated') {
      router.push('/client-auth/signin')
      return
    }

    if (session?.user?.role !== 'CLIENT') {
      router.push('/')
      return
    }

    const loadData = async () => {
      setLoading(true)
      await Promise.all([fetchClientData(), fetchMessages()])
      setLoading(false)
    }
    loadData()
  }, [session, status, router])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ArrowPathIcon className="mx-auto h-12 w-12 text-gray-400 animate-spin" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Loading...</h3>
          <p className="mt-1 text-sm text-gray-500">Please wait while we load your dashboard.</p>
        </div>
      </div>
    )
  }

  // Render functions
  const renderOverview = () => {
    const totalInvoiceAmount = invoices.reduce((sum, invoice) => sum + invoice.totalamount, 0)
    const paidAmount = payments.reduce((sum, payment) => sum + payment.amount, 0)
    const pendingAmount = totalInvoiceAmount - paidAmount
    const activeProjects = projects.filter(p => p.status === 'active' || p.status === 'in_progress').length

    return (
      <div className="space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FolderIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active Projects</dt>
                    <dd className="text-lg font-medium text-gray-900">{activeProjects}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DocumentTextIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Invoices</dt>
                    <dd className="text-lg font-medium text-gray-900">{invoices.length}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CreditCardIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Amount Paid</dt>
                    <dd className="text-lg font-medium text-gray-900">{formatCurrency(paidAmount)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ClockIcon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Pending Amount</dt>
                    <dd className="text-lg font-medium text-gray-900">{formatCurrency(pendingAmount)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
            <div className="space-y-4">
              {projects.slice(0, 3).map((project) => (
                <div key={project.id} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <FolderIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{project.name}</p>
                    <p className="text-sm text-gray-500 truncate">{project.description}</p>
                  </div>
                  <div className="flex-shrink-0">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                      {project.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  const renderProjects = () => {
    const [searchQuery, setSearchQuery] = useState('')
    const [showFilters, setShowFilters] = useState(false)
    const [viewMode, setViewMode] = useState('list')
    const [displayDensity, setDisplayDensity] = useState('comfortable')
    const [showColumnControls, setShowColumnControls] = useState(false)
    const [visibleColumns, setVisibleColumns] = useState(['name', 'status', 'projstartdate', 'estimatecost'])
    const [sortField, setSortField] = useState('')
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

    const availableColumns = [
      { key: 'name', label: 'Project Name', sortable: true },
      { key: 'status', label: 'Status', sortable: true },
      { key: 'projstartdate', label: 'Start Date', sortable: true },
      { key: 'projcompletiondate', label: 'Completion Date', sortable: true },
      { key: 'estimatecost', label: 'Estimated Cost', sortable: true },
      { key: 'estimatetime', label: 'Estimated Time', sortable: false },
      { key: 'updatedat', label: 'Last Updated', sortable: true },
    ]

    const handleSort = (field: string) => {
      const newOrder = sortField === field && sortOrder === 'asc' ? 'desc' : 'asc'
      setSortField(field)
      setSortOrder(newOrder)
    }

    const filteredProjects = projects.filter(project =>
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.description?.toLowerCase().includes(searchQuery.toLowerCase())
    )

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">Projects</h2>
          <button
            onClick={() => setShowQuotationModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Request Quote
          </button>
        </div>

        {/* Enhanced Controls Bar */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search projects..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-3">
                {/* Filters */}
                <div className="relative">
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                      showFilters ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <FunnelIcon className="h-4 w-4 mr-2" />
                    Filters
                  </button>
                </div>

                {/* View Mode */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 text-sm font-medium transition-colors ${
                      viewMode === 'list' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="List view"
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      viewMode === 'grid' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Grid view"
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('card')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      viewMode === 'card' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Card view"
                  >
                    <RectangleStackIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Density */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setDisplayDensity('compact')}
                    className={`px-3 py-2 text-xs font-medium transition-colors ${
                      displayDensity === 'compact' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Compact
                  </button>
                  <button
                    onClick={() => setDisplayDensity('comfortable')}
                    className={`px-3 py-2 text-xs font-medium transition-colors border-l border-gray-300 ${
                      displayDensity === 'comfortable' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Comfortable
                  </button>
                </div>

                {/* Column Controls */}
                <div className="relative">
                  <button
                    onClick={() => setShowColumnControls(!showColumnControls)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                    Columns
                  </button>
                  {showColumnControls && (
                    <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                      <div className="p-3">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Show columns</h4>
                        <div className="space-y-2">
                          {availableColumns.map((column) => (
                            <label key={column.key} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={visibleColumns.includes(column.key)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setVisibleColumns(prev => [...prev, column.key])
                                  } else {
                                    setVisibleColumns(prev => prev.filter(col => col !== column.key))
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{projects.length}</div>
                <div className="text-sm text-gray-600">Total Projects</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {projects.filter(p => p.status === 'in_progress' || p.status === 'active').length}
                </div>
                <div className="text-sm text-gray-600">Active</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {projects.filter(p => p.status === 'completed').length}
                </div>
                <div className="text-sm text-gray-600">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {formatCurrency(projects.reduce((sum, p) => sum + (p.estimatecost || 0), 0))}
                </div>
                <div className="text-sm text-gray-600">Total Value</div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="p-6">
            {filteredProjects.length === 0 ? (
              <div className="text-center py-12">
                <FolderIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No projects found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchQuery ? 'Try adjusting your search' : 'You don\'t have any projects yet.'}
                </p>
              </div>
            ) : (
              <>
                {/* List View */}
                {viewMode === 'list' && (
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {availableColumns
                            .filter(col => visibleColumns.includes(col.key))
                            .map((column) => (
                              <th
                                key={column.key}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                onClick={() => column.sortable && handleSort(column.key)}
                              >
                                <div className="flex items-center space-x-1">
                                  <span>{column.label}</span>
                                  {column.sortable && (
                                    <div className="flex flex-col">
                                      <ChevronUpIcon
                                        className={`h-3 w-3 ${sortField === column.key && sortOrder === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}
                                      />
                                      <ChevronDownIcon
                                        className={`h-3 w-3 -mt-1 ${sortField === column.key && sortOrder === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}
                                      />
                                    </div>
                                  )}
                                </div>
                              </th>
                            ))}
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredProjects.map((project) => (
                          <tr key={project.id} className={displayDensity === 'compact' ? 'h-12' : 'h-16'}>
                            {visibleColumns.includes('name') && (
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">{project.name}</div>
                                    <div className="text-sm text-gray-500">{project.description}</div>
                                  </div>
                                </div>
                              </td>
                            )}
                            {visibleColumns.includes('status') && (
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                                  {project.status}
                                </span>
                              </td>
                            )}
                            {visibleColumns.includes('projstartdate') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {project.projstartdate ? formatDate(project.projstartdate) : '-'}
                              </td>
                            )}
                            {visibleColumns.includes('projcompletiondate') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {project.projcompletiondate ? formatDate(project.projcompletiondate) : '-'}
                              </td>
                            )}
                            {visibleColumns.includes('estimatecost') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {project.estimatecost ? formatCurrency(project.estimatecost) : '-'}
                              </td>
                            )}
                            {visibleColumns.includes('estimatetime') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {project.estimatetime || '-'}
                              </td>
                            )}
                            {visibleColumns.includes('updatedat') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {project.updatedat ? formatDate(project.updatedat) : '-'}
                              </td>
                            )}
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button className="text-blue-600 hover:text-blue-900">
                                <EyeIcon className="h-4 w-4" />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderInvoices = () => {
    const [searchQuery, setSearchQuery] = useState('')
    const [showFilters, setShowFilters] = useState(false)
    const [viewMode, setViewMode] = useState('list')
    const [displayDensity, setDisplayDensity] = useState('comfortable')
    const [showColumnControls, setShowColumnControls] = useState(false)
    const [visibleColumns, setVisibleColumns] = useState(['invoice', 'amount', 'duedate', 'status'])
    const [sortField, setSortField] = useState('')
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

    const availableColumns = [
      { key: 'invoice', label: 'Invoice #', sortable: true },
      { key: 'amount', label: 'Amount', sortable: true },
      { key: 'duedate', label: 'Due Date', sortable: true },
      { key: 'issuedate', label: 'Issue Date', sortable: true },
      { key: 'status', label: 'Status', sortable: true },
      { key: 'description', label: 'Description', sortable: false },
    ]

    const handleSort = (field: string) => {
      const newOrder = sortField === field && sortOrder === 'asc' ? 'desc' : 'asc'
      setSortField(field)
      setSortOrder(newOrder)
    }

    const filteredInvoices = invoices.filter(invoice =>
      String(invoice.id).includes(searchQuery) ||
      invoice.description?.toLowerCase().includes(searchQuery.toLowerCase())
    )

    const totalAmount = invoices.reduce((sum, invoice) => sum + invoice.totalamount, 0)
    const paidAmount = payments.reduce((sum, payment) => sum + payment.amount, 0)
    const pendingAmount = totalAmount - paidAmount

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">Invoices</h2>
        </div>

        {/* Enhanced Controls Bar */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search invoices..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-3">
                {/* Filters */}
                <div className="relative">
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                      showFilters ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <FunnelIcon className="h-4 w-4 mr-2" />
                    Filters
                  </button>
                </div>

                {/* View Mode */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 text-sm font-medium transition-colors ${
                      viewMode === 'list' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="List view"
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      viewMode === 'grid' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Grid view"
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('card')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      viewMode === 'card' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Card view"
                  >
                    <RectangleStackIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Density */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setDisplayDensity('compact')}
                    className={`px-3 py-2 text-xs font-medium transition-colors ${
                      displayDensity === 'compact' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Compact
                  </button>
                  <button
                    onClick={() => setDisplayDensity('comfortable')}
                    className={`px-3 py-2 text-xs font-medium transition-colors border-l border-gray-300 ${
                      displayDensity === 'comfortable' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Comfortable
                  </button>
                </div>

                {/* Column Controls */}
                <div className="relative">
                  <button
                    onClick={() => setShowColumnControls(!showColumnControls)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                    Columns
                  </button>
                  {showColumnControls && (
                    <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                      <div className="p-3">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Show columns</h4>
                        <div className="space-y-2">
                          {availableColumns.map((column) => (
                            <label key={column.key} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={visibleColumns.includes(column.key)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setVisibleColumns(prev => [...prev, column.key])
                                  } else {
                                    setVisibleColumns(prev => prev.filter(col => col !== column.key))
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{invoices.length}</div>
                <div className="text-sm text-gray-600">Total Invoices</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {formatCurrency(totalAmount)}
                </div>
                <div className="text-sm text-gray-600">Total Amount</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(paidAmount)}
                </div>
                <div className="text-sm text-gray-600">Paid Amount</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {formatCurrency(pendingAmount)}
                </div>
                <div className="text-sm text-gray-600">Pending Amount</div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="p-6">
            {filteredInvoices.length === 0 ? (
              <div className="text-center py-12">
                <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchQuery ? 'Try adjusting your search' : 'You don\'t have any invoices yet.'}
                </p>
              </div>
            ) : (
              <>
                {/* List View */}
                {viewMode === 'list' && (
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {availableColumns
                            .filter(col => visibleColumns.includes(col.key))
                            .map((column) => (
                              <th
                                key={column.key}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                onClick={() => column.sortable && handleSort(column.key)}
                              >
                                <div className="flex items-center space-x-1">
                                  <span>{column.label}</span>
                                  {column.sortable && (
                                    <div className="flex flex-col">
                                      <ChevronUpIcon
                                        className={`h-3 w-3 ${sortField === column.key && sortOrder === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}
                                      />
                                      <ChevronDownIcon
                                        className={`h-3 w-3 -mt-1 ${sortField === column.key && sortOrder === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}
                                      />
                                    </div>
                                  )}
                                </div>
                              </th>
                            ))}
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredInvoices.map((invoice) => (
                          <tr key={invoice.id} className={displayDensity === 'compact' ? 'h-12' : 'h-16'}>
                            {visibleColumns.includes('invoice') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                #{invoice.id}
                              </td>
                            )}
                            {visibleColumns.includes('amount') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {formatCurrency(invoice.totalamount)}
                              </td>
                            )}
                            {visibleColumns.includes('duedate') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatDate(invoice.duedate)}
                              </td>
                            )}
                            {visibleColumns.includes('issuedate') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {invoice.issuedate ? formatDate(invoice.issuedate) : '-'}
                              </td>
                            )}
                            {visibleColumns.includes('status') && (
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                                  {invoice.status}
                                </span>
                              </td>
                            )}
                            {visibleColumns.includes('description') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {invoice.description || '-'}
                              </td>
                            )}
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex items-center justify-end space-x-2">
                                <button className="text-blue-600 hover:text-blue-900">
                                  <EyeIcon className="h-4 w-4" />
                                </button>
                                {invoice.status === 'pending' && (
                                  <button
                                    onClick={() => setShowPaymentModal(true)}
                                    className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700"
                                  >
                                    Pay Now
                                  </button>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderPayments = () => {
    const [searchQuery, setSearchQuery] = useState('')
    const [showFilters, setShowFilters] = useState(false)
    const [viewMode, setViewMode] = useState('list')
    const [displayDensity, setDisplayDensity] = useState('comfortable')
    const [showColumnControls, setShowColumnControls] = useState(false)
    const [visibleColumns, setVisibleColumns] = useState(['date', 'amount', 'method', 'invoice', 'status'])
    const [sortField, setSortField] = useState('')
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

    const availableColumns = [
      { key: 'date', label: 'Payment Date', sortable: true },
      { key: 'amount', label: 'Amount', sortable: true },
      { key: 'method', label: 'Payment Method', sortable: true },
      { key: 'invoice', label: 'Invoice', sortable: false },
      { key: 'status', label: 'Status', sortable: true },
    ]

    const handleSort = (field: string) => {
      const newOrder = sortField === field && sortOrder === 'asc' ? 'desc' : 'asc'
      setSortField(field)
      setSortOrder(newOrder)
    }

    const filteredPayments = payments.filter(payment =>
      String(payment.id).includes(searchQuery) ||
      payment.paymentmethod?.toLowerCase().includes(searchQuery.toLowerCase())
    )

    const totalPayments = payments.reduce((sum, payment) => sum + payment.amount, 0)
    const recentPayments = payments.filter(p => {
      const paymentDate = new Date(p.paymentdate)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
      return paymentDate >= thirtyDaysAgo
    }).length

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">Payments</h2>
          <button
            onClick={() => setShowPaymentModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Payment
          </button>
        </div>

        {/* Enhanced Controls Bar */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search payments..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-3">
                {/* Filters */}
                <div className="relative">
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                      showFilters ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <FunnelIcon className="h-4 w-4 mr-2" />
                    Filters
                  </button>
                </div>

                {/* View Mode */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 text-sm font-medium transition-colors ${
                      viewMode === 'list' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="List view"
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      viewMode === 'grid' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Grid view"
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('card')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      viewMode === 'card' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Card view"
                  >
                    <RectangleStackIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Density */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setDisplayDensity('compact')}
                    className={`px-3 py-2 text-xs font-medium transition-colors ${
                      displayDensity === 'compact' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Compact
                  </button>
                  <button
                    onClick={() => setDisplayDensity('comfortable')}
                    className={`px-3 py-2 text-xs font-medium transition-colors border-l border-gray-300 ${
                      displayDensity === 'comfortable' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Comfortable
                  </button>
                </div>

                {/* Column Controls */}
                <div className="relative">
                  <button
                    onClick={() => setShowColumnControls(!showColumnControls)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                    Columns
                  </button>
                  {showColumnControls && (
                    <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                      <div className="p-3">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Show columns</h4>
                        <div className="space-y-2">
                          {availableColumns.map((column) => (
                            <label key={column.key} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={visibleColumns.includes(column.key)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setVisibleColumns(prev => [...prev, column.key])
                                  } else {
                                    setVisibleColumns(prev => prev.filter(col => col !== column.key))
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{payments.length}</div>
                <div className="text-sm text-gray-600">Total Payments</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(totalPayments)}
                </div>
                <div className="text-sm text-gray-600">Total Amount</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {recentPayments}
                </div>
                <div className="text-sm text-gray-600">Recent (30 days)</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {payments.filter(p => p.status === 'completed').length}
                </div>
                <div className="text-sm text-gray-600">Completed</div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="p-6">
            {filteredPayments.length === 0 ? (
              <div className="text-center py-12">
                <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No payments found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchQuery ? 'Try adjusting your search' : 'You haven\'t made any payments yet.'}
                </p>
              </div>
            ) : (
              <>
                {/* List View */}
                {viewMode === 'list' && (
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {availableColumns
                            .filter(col => visibleColumns.includes(col.key))
                            .map((column) => (
                              <th
                                key={column.key}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                onClick={() => column.sortable && handleSort(column.key)}
                              >
                                <div className="flex items-center space-x-1">
                                  <span>{column.label}</span>
                                  {column.sortable && (
                                    <div className="flex flex-col">
                                      <ChevronUpIcon
                                        className={`h-3 w-3 ${sortField === column.key && sortOrder === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}
                                      />
                                      <ChevronDownIcon
                                        className={`h-3 w-3 -mt-1 ${sortField === column.key && sortOrder === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}
                                      />
                                    </div>
                                  )}
                                </div>
                              </th>
                            ))}
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredPayments.map((payment) => (
                          <tr key={payment.id} className={displayDensity === 'compact' ? 'h-12' : 'h-16'}>
                            {visibleColumns.includes('date') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatDate(payment.paymentdate)}
                              </td>
                            )}
                            {visibleColumns.includes('amount') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {formatCurrency(payment.amount)}
                              </td>
                            )}
                            {visibleColumns.includes('method') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {payment.paymentmethod}
                              </td>
                            )}
                            {visibleColumns.includes('invoice') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                #{payment.invoiceid}
                              </td>
                            )}
                            {visibleColumns.includes('status') && (
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                                  {payment.status}
                                </span>
                              </td>
                            )}
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button className="text-blue-600 hover:text-blue-900">
                                <EyeIcon className="h-4 w-4" />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderQuotations = () => {
    const [searchQuery, setSearchQuery] = useState('')
    const [showFilters, setShowFilters] = useState(false)
    const [viewMode, setViewMode] = useState('list')
    const [displayDensity, setDisplayDensity] = useState('comfortable')
    const [showColumnControls, setShowColumnControls] = useState(false)
    const [visibleColumns, setVisibleColumns] = useState(['service', 'description', 'status', 'date', 'budget'])
    const [sortField, setSortField] = useState('')
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')

    const availableColumns = [
      { key: 'service', label: 'Service', sortable: true },
      { key: 'description', label: 'Description', sortable: false },
      { key: 'status', label: 'Status', sortable: true },
      { key: 'date', label: 'Request Date', sortable: true },
      { key: 'budget', label: 'Budget Range', sortable: true },
    ]

    const handleSort = (field: string) => {
      const newOrder = sortField === field && sortOrder === 'asc' ? 'desc' : 'asc'
      setSortField(field)
      setSortOrder(newOrder)
    }

    const filteredQuotations = quotations.filter(quotation =>
      quotation.servicename?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      quotation.description?.toLowerCase().includes(searchQuery.toLowerCase())
    )

    const totalQuotations = quotations.length
    const pendingQuotations = quotations.filter(q => q.status === 'pending').length
    const approvedQuotations = quotations.filter(q => q.status === 'approved').length

    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900">Quotations</h2>
          <button
            onClick={() => setShowQuotationModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Request Quote
          </button>
        </div>

        {/* Enhanced Controls Bar */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search quotations..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-3">
                {/* Filters */}
                <div className="relative">
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                      showFilters ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <FunnelIcon className="h-4 w-4 mr-2" />
                    Filters
                  </button>
                </div>

                {/* View Mode */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 text-sm font-medium transition-colors ${
                      viewMode === 'list' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="List view"
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      viewMode === 'grid' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Grid view"
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('card')}
                    className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                      viewMode === 'card' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                    title="Card view"
                  >
                    <RectangleStackIcon className="h-4 w-4" />
                  </button>
                </div>

                {/* Density */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setDisplayDensity('compact')}
                    className={`px-3 py-2 text-xs font-medium transition-colors ${
                      displayDensity === 'compact' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Compact
                  </button>
                  <button
                    onClick={() => setDisplayDensity('comfortable')}
                    className={`px-3 py-2 text-xs font-medium transition-colors border-l border-gray-300 ${
                      displayDensity === 'comfortable' ? 'bg-blue-50 text-blue-700' : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    Comfortable
                  </button>
                </div>

                {/* Column Controls */}
                <div className="relative">
                  <button
                    onClick={() => setShowColumnControls(!showColumnControls)}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                    Columns
                  </button>
                  {showColumnControls && (
                    <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                      <div className="p-3">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Show columns</h4>
                        <div className="space-y-2">
                          {availableColumns.map((column) => (
                            <label key={column.key} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={visibleColumns.includes(column.key)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setVisibleColumns(prev => [...prev, column.key])
                                  } else {
                                    setVisibleColumns(prev => prev.filter(col => col !== column.key))
                                  }
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{totalQuotations}</div>
                <div className="text-sm text-gray-600">Total Requests</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {pendingQuotations}
                </div>
                <div className="text-sm text-gray-600">Pending</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {approvedQuotations}
                </div>
                <div className="text-sm text-gray-600">Approved</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {quotations.filter(q => q.status === 'completed').length}
                </div>
                <div className="text-sm text-gray-600">Completed</div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="p-6">
            {filteredQuotations.length === 0 ? (
              <div className="text-center py-12">
                <BanknotesIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No quotations found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchQuery ? 'Try adjusting your search' : 'You haven\'t requested any quotations yet.'}
                </p>
                <div className="mt-6">
                  <button
                    onClick={() => setShowQuotationModal(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Request Your First Quote
                  </button>
                </div>
              </div>
            ) : (
              <>
                {/* List View */}
                {viewMode === 'list' && (
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {availableColumns
                            .filter(col => visibleColumns.includes(col.key))
                            .map((column) => (
                              <th
                                key={column.key}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                onClick={() => column.sortable && handleSort(column.key)}
                              >
                                <div className="flex items-center space-x-1">
                                  <span>{column.label}</span>
                                  {column.sortable && (
                                    <div className="flex flex-col">
                                      <ChevronUpIcon
                                        className={`h-3 w-3 ${sortField === column.key && sortOrder === 'asc' ? 'text-blue-600' : 'text-gray-400'}`}
                                      />
                                      <ChevronDownIcon
                                        className={`h-3 w-3 -mt-1 ${sortField === column.key && sortOrder === 'desc' ? 'text-blue-600' : 'text-gray-400'}`}
                                      />
                                    </div>
                                  )}
                                </div>
                              </th>
                            ))}
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {filteredQuotations.map((quotation) => (
                          <tr key={quotation.id} className={displayDensity === 'compact' ? 'h-12' : 'h-16'}>
                            {visibleColumns.includes('service') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {quotation.servicename}
                              </td>
                            )}
                            {visibleColumns.includes('description') && (
                              <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                                {quotation.description || '-'}
                              </td>
                            )}
                            {visibleColumns.includes('status') && (
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(quotation.status)}`}>
                                  {quotation.status}
                                </span>
                              </td>
                            )}
                            {visibleColumns.includes('date') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {quotation.requestdate ? formatDate(quotation.requestdate) : '-'}
                              </td>
                            )}
                            {visibleColumns.includes('budget') && (
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {quotation.budgetrange || '-'}
                              </td>
                            )}
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button className="text-blue-600 hover:text-blue-900">
                                <EyeIcon className="h-4 w-4" />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  const renderMessages = () => (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Messages</h3>
          <div className="text-center py-8">
            <ChatBubbleLeftIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No messages</h3>
            <p className="mt-1 text-sm text-gray-500">Your message history will appear here.</p>
          </div>
        </div>
      </div>
    </div>
  )

  const renderSettings = () => (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Account Settings</h3>
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Profile Information</label>
              <div className="mt-1">
                <button
                  onClick={() => setShowProfileModal(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
                >
                  Edit Profile
                </button>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Password</label>
              <div className="mt-1">
                <button
                  onClick={() => setShowPasswordModal(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
                >
                  Change Password
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <Toaster position="top-right" />
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Client Dashboard</h1>
                <p className="mt-1 text-sm text-gray-500">
                  Welcome back, {client?.contactname || 'Client'}
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <BuildingOfficeIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-900">{client?.companyname}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                  <span className="text-sm text-gray-900">{client?.contactemail}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar Navigation */}
            <div className="lg:w-64 flex-shrink-0">
              <nav className="space-y-1">
                {dashboardSections.map((section) => {
                  const Icon = section.icon
                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={`w-full group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        activeSection === section.id
                          ? 'bg-blue-100 text-blue-700 border-blue-300'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <Icon
                        className={`flex-shrink-0 -ml-1 mr-3 h-5 w-5 ${
                          activeSection === section.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                        }`}
                      />
                      <span className="truncate">{section.name}</span>
                    </button>
                  )
                })}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1 min-w-0">
              <motion.div
                key={activeSection}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                {activeSection === 'overview' && renderOverview()}
                {activeSection === 'projects' && renderProjects()}
                {activeSection === 'invoices' && renderInvoices()}
                {activeSection === 'payments' && renderPayments()}
                {activeSection === 'quotations' && renderQuotations()}
                {activeSection === 'messages' && renderMessages()}
                {activeSection === 'settings' && renderSettings()}
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal Components */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        invoices={invoices}
        onSubmit={async (paymentData) => {
          try {
            const response = await fetch('/api/payments', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                ...paymentData,
                clientId: client?.id
              })
            })

            if (response.ok) {
              toast.success('Payment submitted successfully!')
              fetchClientData() // Refresh data
            } else {
              throw new Error('Payment failed')
            }
          } catch (error) {
            console.error('Payment error:', error)
            toast.error('Failed to process payment')
            throw error
          }
        }}
      />

      <QuotationModal
        isOpen={showQuotationModal}
        onClose={() => setShowQuotationModal(false)}
        services={services}
        onSubmit={async (quotationData) => {
          try {
            const response = await fetch('/api/quotations', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                ...quotationData,
                clientId: client?.id
              })
            })

            if (response.ok) {
              toast.success('Quotation request submitted successfully!')
              // Refresh quotations data if needed
            } else {
              throw new Error('Quotation submission failed')
            }
          } catch (error) {
            console.error('Quotation error:', error)
            toast.error('Failed to submit quotation request')
            throw error
          }
        }}
      />

      <PasswordModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        onSubmit={async (passwordData) => {
          try {
            const response = await fetch('/api/auth/change-password', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(passwordData)
            })

            if (response.ok) {
              toast.success('Password changed successfully! Please sign in again.')
              // Sign out user after password change
              setTimeout(() => {
                window.location.href = '/client-auth/signin'
              }, 2000)
            } else {
              const error = await response.json()
              throw new Error(error.message || 'Password change failed')
            }
          } catch (error) {
            console.error('Password change error:', error)
            toast.error(error.message || 'Failed to change password')
            throw error
          }
        }}
      />

      <ProfileModal
        isOpen={showProfileModal}
        onClose={() => setShowProfileModal(false)}
        client={client}
        onSubmit={async (profileData) => {
          try {
            const response = await fetch(`/api/clients/${client?.id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(profileData)
            })

            if (response.ok) {
              toast.success('Profile updated successfully!')
              fetchClientData() // Refresh client data
            } else {
              throw new Error('Profile update failed')
            }
          } catch (error) {
            console.error('Profile update error:', error)
            toast.error('Failed to update profile')
            throw error
          }
        }}
      />
    </div>
  )
}
